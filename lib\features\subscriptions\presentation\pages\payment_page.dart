import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/toast_dialog.dart';
import 'package:cal/features/subscriptions/presentation/widgets/trial_timeline_widget.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cal/features/subscriptions/presentation/bloc/subscription_bloc.dart';
import 'package:cal/features/subscriptions/presentation/widgets/payment_plan_card.dart';
import 'package:cal/features/subscriptions/enums.dart';
import 'package:toastification/toastification.dart';

class PaymentPage extends StatefulWidget {
  const PaymentPage({super.key});

  @override
  State<PaymentPage> createState() => _PaymentPageState();
}

class _PaymentPageState extends State<PaymentPage> {
  @override
  void initState() {
    super.initState();
    context.read<SubscriptionBloc>().add(const InitializeSubscriptions());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextButton(
          onPressed: () {
            context.read<SubscriptionBloc>().add(const RestorePurchases());
          },
          child: Text(
            LocaleKeys.payment_restore.tr(),
            style: context.textTheme.bodyMedium,
          ),
        ),
      ),
      body: BlocListener<SubscriptionBloc, SubscriptionState>(
        listener: (context, state) {
          if (state.status == SubscriptionPurchaseStatus.error) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'حدث خطأ'),
                backgroundColor: Colors.red,
              ),
            );
            Navigator.of(context).pushNamed("/exit_promo");
          }
        },
        child: BlocBuilder<SubscriptionBloc, SubscriptionState>(
          builder: (context, state) {
            final hasPlans = state.subscriptionPlans.isNotEmpty;

            return SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (hasPlans) ...[
                    Text(
                      LocaleKeys.payment_start_3_days_free.tr(),
                      textAlign: TextAlign.center,
                      style: context.textTheme.headlineMedium,
                    ),
                    const SizedBox(height: 18),
                    const TimelineWidget(),
                    const SizedBox(height: 36),
                  ],

                  // Subscription Plans
                  _buildSubscriptionPlans(context, state),

                  if (hasPlans) ...[
                    const SizedBox(height: 20),

                    // No payment due text
                    Text(
                      LocaleKeys.payment_no_payment_due.tr(),
                      style: context.textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 30),

                    // Start trial button
                    _buildStartTrialButton(context, state),
                    const SizedBox(height: 20),
                  ],
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSubscriptionPlans(BuildContext context, SubscriptionState state) {
    if (state.subscriptionPlans.isEmpty) {
      return Center(
        child: state.errorMessage != null && state.errorMessage!.isNotEmpty
            ? Text(
                state.errorMessage!,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              )
            : CircularProgressIndicator(
                color: context.primaryColor,
              ),
      );
    }

    // Find yearly and monthly plans
    final yearlyPlan = state.subscriptionPlans.firstWhere(
      (plan) => plan.type == SubscriptionPlanType.yearly,
      orElse: () => state.subscriptionPlans.first,
    );

    final monthlyPlan = state.subscriptionPlans.firstWhere(
      (plan) => plan.type == SubscriptionPlanType.monthly,
      orElse: () => state.subscriptionPlans.last,
    );

    return Row(
      children: [
        Expanded(
          child: PaymentPlanCard(
            plan: yearlyPlan,
            isSelected: state.selectedPlan?.id == yearlyPlan.id,
            onTap: () {
              context.read<SubscriptionBloc>().add(SelectSubscriptionPlan(plan: yearlyPlan));
            },
            badge: LocaleKeys.payment_free_for_3_days.tr(),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: PaymentPlanCard(
            plan: monthlyPlan,
            isSelected: state.selectedPlan?.id == monthlyPlan.id,
            onTap: () {
              context.read<SubscriptionBloc>().add(SelectSubscriptionPlan(plan: monthlyPlan));
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStartTrialButton(BuildContext context, SubscriptionState state) {
    final isLoading = state.status == SubscriptionPurchaseStatus.loading;

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: isLoading
            ? null
            : () {
                if (state.selectedPlan != null) {
                  context.read<SubscriptionBloc>().add(const PurchaseSubscription());
                } else {
                  ToastificationDialog.showToast(
                      msg: LocaleKeys.payment_choose_subscription_plan.tr(), context: context, type: ToastificationType.error);
                }
              },
        style: ElevatedButton.styleFrom(
          backgroundColor: context.primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                LocaleKeys.payment_start_3_days_free.tr(),
                style: context.textTheme.titleSmall,
              ),
      ),
    );
  }
}
