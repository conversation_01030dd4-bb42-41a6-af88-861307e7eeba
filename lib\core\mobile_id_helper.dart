import 'package:cal/common/utils/shared_preferences_helper.dart';
import 'package:uuid/uuid.dart';

class MobileIdHelper {
  static const _storageKey = 'device_mobile_id';

  static Future<String> getMobileId() async {
    final storedId = await ShPH.getData(key: _storageKey);
    if (storedId != null) return storedId;

    final newId = const Uuid().v4();
    await await ShPH.saveData(key: _storageKey, value: newId);
    return newId;
  }
}
