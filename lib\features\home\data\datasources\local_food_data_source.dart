import 'dart:io';

import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:isar/isar.dart';

@injectable
class LocalFoodDataSource {
  final Isar _isar;

  LocalFoodDataSource(this._isar);

  Future<void> saveMeal(FoodModel food) async {
    await _isar.writeTxn(() async {
      await _isar.foodModels.put(food);
    });
  }

  Future<List<FoodModel>> getAllMeals(DateTime date) async {
    return await _isar.foodModels
        .filter()
        .dateBetween(DateTime(date.year, date.month, date.day), DateTime(date.year, date.month, date.day + 1))
        .findAll();
  }

  Future<void> updateFood(FoodModel updatedFood) async {
    await _isar.writeTxn(() async {
      await _isar.foodModels.put(updatedFood);
    });
  }

  Future<void> deleteFood(FoodModel food) async {
    if (food.imagePath != null && food.imagePath!.isNotEmpty) {
      try {
        final imageFile = File(food.imagePath!);
        if (await imageFile.exists()) {
          await imageFile.delete();
          if (kDebugMode) {
            print('Deleted image at: ${food.imagePath}');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error deleting image: $e');
        }
      }
    }
    await _isar.writeTxn(() async {
      await _isar.foodModels.delete(food.id);
    });
  }

  Future<void> clearMeals() async {
    await _isar.writeTxn(
      () async {
        await _isar.foodModels.clear();
      },
    );
  }
}
