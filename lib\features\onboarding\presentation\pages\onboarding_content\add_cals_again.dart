import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Goal Selection Screen
class AddCalsAgain extends StatefulWidget {
  const AddCalsAgain({super.key});

  @override
  State<AddCalsAgain> createState() => _GoalSelectionContentState();
}

class _GoalSelectionContentState extends State<AddCalsAgain> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.settings_Add_the_calories_burned_back_to_the_daily_goal.tr(),
          centerContent: true,
          contentWidgets: [
            Stack(
              children: [
                const AppImage.asset(Assets.onboardingRunning, size: 430),
                Positioned(
                  right: 30,
                  bottom: 20,
                  child: Container(
                      width: 113,
                      height: 104,
                      decoration: BoxDecoration(
                        color: context.onPrimaryColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 3.0, horizontal: 3),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Text(
                              LocaleKeys.onboarding_daily_target.tr(),
                              style: context.textTheme.labelMedium!.copyWith(
                                color: context.primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Row(
                              spacing: 4,
                              children: [
                                const AppImage.asset(Assets.imagesCals),
                                Text(
                                  "85",
                                  style: context.textTheme.labelSmall!
                                      .copyWith(color: Colors.black, fontWeight: FontWeight.bold),
                                ),
                                Text(
                                  LocaleKeys.onboarding_calorie.tr(),
                                  style: context.textTheme.labelSmall!
                                      .copyWith(color: Colors.black, fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            Row(
                              spacing: 4,
                              children: [
                                const AppImage.asset(Assets.onboardingShose, size: 20),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      LocaleKeys.onboarding_run.tr(),
                                      style: context.textTheme.labelSmall!.copyWith(
                                          color: context.onSecondary.withAlpha(140), fontWeight: FontWeight.bold),
                                    ),
                                    Row(
                                      spacing: 4,
                                      children: [
                                        Text(
                                          "+100",
                                          style: context.textTheme.labelSmall!
                                              .copyWith(color: Colors.black, fontWeight: FontWeight.bold),
                                        ),
                                        Text(
                                          LocaleKeys.onboarding_cal.tr(),
                                          style: context.textTheme.labelSmall!
                                              .copyWith(color: Colors.black, fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      )),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
