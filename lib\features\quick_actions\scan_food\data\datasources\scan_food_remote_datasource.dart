import 'package:cal/core/config/endpoints.dart';
import 'package:cal/core/network/api_handler.dart';
import 'package:cal/core/network/exceptions.dart';
import 'package:cal/core/network/http_client.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/quick_actions/scan_food/domain/usecases/recognize_food_usecase.dart';
import 'package:cal/features/quick_actions/scan_food/domain/usecases/scan_barcode_use_case.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class ScanFoodRemoteDataSource with ApiHandler {
  final HTTPClient httpClient;

  ScanFoodRemoteDataSource({required this.httpClient});

  Future<Either<Failure, FoodModel>> recognizeFood({required AnalyzeFoodParams analyzeFoodParams}) async {
    return handleApiCall(
      apiCall: () => httpClient.post(AppEndPoint.analysis, data: analyzeFoodParams.getBody()),
      fromJson: (json) => FoodModel.fromJson(json),
    );
  }

  Future<Either<Failure, FoodModel>> scanBarcode({required ScanBarcodeParams params}) async {
    return handleApiCall(
      apiCall: () => httpClient.get('${AppEndPoint.scanBarcode}?code=${params.barcode}'),
      fromJson: (json) => foodModelFromJson(json),
    );
  }
}
