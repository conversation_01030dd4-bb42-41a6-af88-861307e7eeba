import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart';
import 'package:cal/features/quick_actions/food_database/presentation/pages/food_database_screen.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/bloc/scan_food_bloc.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/widgets/scan_camera_widget.dart';
import 'package:cal/features/quick_actions/exercise/presentation/bloc/exercise_bloc.dart';
import 'package:cal/features/quick_actions/exercise/presentation/pages/log_exercise_screen.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart' show Colors;
import 'package:flutter_bloc/flutter_bloc.dart';

class QuickActionsGrid extends StatefulWidget {
  final VoidCallback onActionSelected;
  final bool isVisible;

  const QuickActionsGrid({
    required this.onActionSelected,
    required this.isVisible,
    super.key,
  });

  @override
  State<QuickActionsGrid> createState() => _QuickActionsGridState();
}

class _QuickActionsGridState extends State<QuickActionsGrid> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _scaleAnimation = Tween<double>(
      begin: 0.7,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutBack));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeIn));

    if (widget.isVisible) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(QuickActionsGrid oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: child,
          ),
        );
      },
      child: Container(
        constraints: BoxConstraints(maxWidth: context.screenWidth * 0.9),
        child: GridView.count(
          shrinkWrap: true,
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          children: [
            QuickAction(
                label: LocaleKeys.quick_actions_scan_food.tr(),
                iconImagePath: Assets.quickActionsScanFood,
                onTap: () {
                  widget.onActionSelected();
                  context.push(
                    BlocProvider.value(
                      value: context.read<ScanFoodBloc>(),
                      child: const ScanCameraWidget(),
                    ),
                  );
                }),
            QuickAction(
              label: LocaleKeys.quick_actions_food_database.tr(),
              iconImagePath: Assets.quickActionsFoodDatabase,
              onTap: () {
                widget.onActionSelected();
                context.push(BlocProvider(
                  create: (context) => getIt<FoodDatabaseBloc>(),
                  child: const FoodDatabaseScreen(),
                ));
              },
            ),
            QuickAction(
              label: LocaleKeys.quick_actions_add_exercise.tr(),
              iconImagePath: Assets.quickActionsLogExercise,
              onTap: () {
                widget.onActionSelected();
                context.push(BlocProvider(
                  create: (context) => getIt<ExerciseBloc>(),
                  child: const LogExerciseScreen(),
                ));
              },
            ),
            QuickAction(
              label: LocaleKeys.quick_actions_saved_food.tr(),
              iconImagePath: Assets.quickActionsLogExercise,
              onTap: () {
                widget.onActionSelected();
                context.push(BlocProvider(
                  create: (context) => getIt<FoodDatabaseBloc>(),
                  child: const FoodDatabaseScreen(
                    tapIndex: 2,
                  ),
                ));
              },
            ),
          ],
        ),
      ),
    );
  }
}

class QuickAction extends StatelessWidget {
  final String iconImagePath;
  final String label;
  final VoidCallback onTap;

  const QuickAction({
    required this.iconImagePath,
    required this.label,
    required this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return AppGestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: context.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 10)],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(color: context.onPrimaryContainer, shape: BoxShape.circle),
              child: Image.asset(iconImagePath, width: 28, height: 28),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
