import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class OnboardingOption extends StatelessWidget {
  final String text;
  final String? secondaryText;
  final bool isTextCentered;
  final String? imagePath;
  final VoidCallback? onSelected;
  final bool isSelected;
  final bool doesColorChange;

  const OnboardingOption({
    super.key,
    required this.text,
    this.secondaryText,
    this.isTextCentered = false,
    this.imagePath,
    this.onSelected,
    this.isSelected = false,
    this.doesColorChange = true,
  });

  @override
  Widget build(BuildContext context) {
    Color getColor() => isSelected ? context.primaryColor : Colors.black.withAlpha(178);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: getColor(), width: 2),
        borderRadius: BorderRadius.circular(10),
      ),
      child: ListTile(
        contentPadding: imagePath == null ? const EdgeInsets.symmetric(horizontal: 16) : null,
        leading: imagePath != null
            ? AppImage.asset(
                imagePath!,
                color: doesColorChange ? getColor() : null,
                size: 24,
              )
            : null,
        titleAlignment: ListTileTitleAlignment.center,
        title: Column(
          crossAxisAlignment: isTextCentered ? CrossAxisAlignment.center : CrossAxisAlignment.start,
          children: [
            if (secondaryText != null)
              Text(
                secondaryText!,
                style: context.textTheme.bodySmall!.copyWith(color: getColor()),
                textAlign: isTextCentered ? TextAlign.center : TextAlign.start,
              ),
            Text(
              text,
              style: context.textTheme.bodyMedium!.copyWith(color: getColor()),
              textAlign: isTextCentered ? TextAlign.center : TextAlign.start,
            ),
          ],
        ),
        onTap: () async {
          await HapticFeedback.selectionClick();

          onSelected != null ? onSelected!() : null;
        },
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}
