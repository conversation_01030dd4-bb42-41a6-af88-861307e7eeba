import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/core/local_models/user_model/user_model.dart';
import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'package:throttling/throttling.dart';

import '../../../../common/widgets/app_text.dart';
import '../../../../common/widgets/large_button.dart';
import '../../../../core/datasources/user_local_data_source.dart';
import '../../../../core/di/injection.dart';
import '../../../onboarding/presentation/widgets/birthdate_selector.dart';

class ChangeBirthDateScreen extends StatefulWidget {
  const ChangeBirthDateScreen({super.key, required this.user});

  final UserModel user;

  @override
  State<ChangeBirthDateScreen> createState() => _ChangeBirthDateScreenState();
}

class _ChangeBirthDateScreenState extends State<ChangeBirthDateScreen> {
  DateTime birth = DateTime.now();

  @override
  void initState() {
    super.initState();
    birth = widget.user.birthDate ?? DateTime.now();
  }

  Throttling throttler = Throttling(duration: const Duration(milliseconds: 300));

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsetsDirectional.symmetric(horizontal: 20),
          child: Column(
            children: [
              const SizedBox(height: 30),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    borderRadius: BorderRadius.circular(50),
                    onTap: () {
                      context.pop();
                    },
                    child: CircleAvatar(
                      radius: 25,
                      backgroundColor: context.onSecondary.withAlpha(21),
                      child: Icon(
                        Icons.arrow_back,
                        color: context.onSecondary,
                        size: 18,
                      ),
                    ),
                  ),
                  AppText.titleSmall('Edit birth date', color: context.onSecondary, fontWeight: FontWeight.w700),
                  const CircleAvatar(
                    radius: 25,
                    backgroundColor: Colors.transparent,
                    child: Icon(
                      Icons.arrow_back,
                      color: Colors.transparent,
                      size: 18,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 100),
              BirthDateSelector(
                initialDay: birth.day,
                initialMonth: birth.month,
                initialYear: birth.year,
                onDayChanged: (day) {
                  throttler.throttle(() {
                    setState(() {
                      birth = birth.copyWith(day: day);
                    });
                  });
                },
                onMonthChanged: (month) {
                  throttler.throttle(() {
                    setState(() {
                      birth = birth.copyWith(month: month);
                    });
                  });
                },
                onYearChanged: (year) {
                  throttler.throttle(() {
                    setState(() {
                      birth = birth.copyWith(year: year);
                    });
                  });
                },
              ),
              const Spacer(),
              LargeButton(
                onPressed: () async {
                  await UserLocalDataSource(getIt<Isar>()).saveUserData(widget.user.copyWith(birthDate: birth)).then((val) async {
                    if (context.mounted) {
                      context.pop(true);
                    }
                  });
                },
                text: 'Save Changes',
                backgroundColor: context.primaryColor,
                circularRadius: 16,
                textStyle: context.textTheme.bodyMedium!.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
