import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class ExitPromoPage extends StatelessWidget {
  const ExitPromoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            Navigator.of(context).pop(); // Pop back to the previous screen in the nested navigator
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              LocaleKeys.payment_one_time_offer.tr(), // "One Time Offer"
              textAlign: TextAlign.center,
              style: context.textTheme.headlineMedium,
            ),
            const SizedBox(height: 10),
            Text(
              LocaleKeys.payment_never_see_again.tr(), // "You will never see this again"
              textAlign: TextAlign.center,
              style: context.textTheme.bodyMedium,
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Text(
                    LocaleKeys.payment_discount_message.tr(), // "Here's a 80% off discount"
                    style: context.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 10),
                  Text(
                    LocaleKeys.payment_promo_price.tr(), // "Only $1.66 / month"
                    style: context.textTheme.headlineLarge!.copyWith(color: context.primaryColor),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    LocaleKeys.payment_lowest_price_ever.tr(), // "Lowest price ever"
                    style: context.textTheme.bodySmall,
                  ),
                ],
              ),
            ),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: () {
                  // TODO: Implement claim offer logic
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.primaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  LocaleKeys.payment_claim_offer.tr(), // "Claim your limited offer now!"
                  style: context.textTheme.titleSmall,
                ),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
