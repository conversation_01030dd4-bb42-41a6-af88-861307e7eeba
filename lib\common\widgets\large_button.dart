import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:flutter/material.dart';

class LargeButton extends StatelessWidget {
  final void Function() onPressed;
  final String text;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final bool isFlexible;
  final bool isOutlined;
  final double circularRadius;
  final double? height;
  final double? width;

  const LargeButton({
    required this.onPressed,
    required this.text,
    this.textStyle,
    this.backgroundColor,
    this.isFlexible = false,
    this.isOutlined = false,
    this.circularRadius = 5.0,
    this.height,
    this.width,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final kTextStyle = Theme.of(context)
        .textTheme
        .bodyMedium!
        .copyWith(color: Theme.of(context).colorScheme.primary, fontWeight: FontWeight.bold);
    final jTextStyle = Theme.of(context)
        .textTheme
        .bodyMedium!
        .copyWith(color: Theme.of(context).colorScheme.onPrimary, fontWeight: FontWeight.bold);

    return AppGestureDetector(
      onTap: onPressed,
      child: Container(
        height: height,
        width: width,
        padding: !isFlexible ? const EdgeInsets.symmetric(vertical: 12.0, horizontal: 0.0) : null,
        decoration: !isOutlined
            ? BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(circularRadius),
              )
            : BoxDecoration(
                color: backgroundColor,
                // border: Border.all(color: context.primaryColor, width: 1.5),
                borderRadius: BorderRadius.circular(circularRadius),
              ),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Text(
              text,
              textAlign: TextAlign.center,
              style: !isOutlined ? (textStyle ?? kTextStyle) : jTextStyle,
            ),
          ),
        ),
      ),
    );
  }
}
