import 'package:equatable/equatable.dart';
import 'package:dio/dio.dart';

class ExerciseSaveModel extends Equatable {
  final int calories;
  final String intensity;
  final int duration;
  final String typeArabic;
  final String typeEnglish;

  const ExerciseSaveModel({
    required this.calories,
    required this.intensity,
    required this.duration,
    required this.typeArabic,
    required this.typeEnglish,
  });

  /// Convert to form data for API submission
  FormData toFormData() {
    return FormData.fromMap({
      "calories": calories,
      "intensity": intensity,
      "duration": duration,
      "ar_type": typeArabic,
      "en_type": typeEnglish,
    });
  }

  /// Convert to JSON for local storage or debugging
  Map<String, dynamic> toJson() {
    return {
      "calories": calories,
      "intensity": intensity,
      "duration": duration,
      "ar_type": typeArabic,
      "en_type": typeEnglish,
    };
  }

  @override
  List<Object?> get props => [calories, intensity, duration, typeArabic, typeEnglish];
}
