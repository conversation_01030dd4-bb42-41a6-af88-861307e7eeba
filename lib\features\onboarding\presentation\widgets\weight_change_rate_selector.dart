import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:throttling/throttling.dart';

class WeightChangeRateSelector extends StatefulWidget {
  final double initialRate;
  final Function(double) onRateChanged;
  final Goal goal;

  const WeightChangeRateSelector({
    super.key,
    required this.initialRate,
    required this.onRateChanged,
    required this.goal,
  });

  @override
  State<WeightChangeRateSelector> createState() => _WeightChangeRateSelectorState();
}

class _WeightChangeRateSelectorState extends State<WeightChangeRateSelector> {
  late double _currentRate;
  late bool _isWeightLoss;
  late Throttling _throttler;

  @override
  void initState() {
    super.initState();
    _currentRate = widget.initialRate;
    _isWeightLoss = widget.goal == Goal.weightLoss;
    _throttler = Throttling(duration: const Duration(milliseconds: 300));
  }

  @override
  void didUpdateWidget(WeightChangeRateSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.goal != widget.goal) {
      _isWeightLoss = widget.goal == Goal.weightLoss;
    }
  }

  @override
  void dispose() {
    _throttler.close();
    super.dispose();
  }

  String _getRateMessage() {
    if (_currentRate < 0.5) {
      return LocaleKeys.onboarding_slow.tr(); // Add this to your translations
    } else if (_currentRate > 1) {
      return LocaleKeys.onboarding_fast.tr(); // Add this as well
    } else {
      return LocaleKeys.onboarding_ideal_rate.tr(); // Add this too
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          "$_currentRate ${LocaleKeys.onboarding_kg.tr()}",
          style: context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppImage.asset(Assets.onboardingSloth, size: 35),
              AppImage.asset(Assets.onboardingRabbet, size: 35),
              AppImage.asset(Assets.onboardingCheetah, size: 35),
            ],
          ),
        ),
        Row(
          children: [
            // Image.asset(_isWeightLoss ? 'assets/images/tortoise.png' : 'assets/images/snail.png', height: 24),
            Expanded(
              child: SliderTheme(
                data: SliderThemeData(
                  activeTrackColor: context.onSecondaryContainer,
                  inactiveTrackColor: Colors.grey.shade300,
                  thumbColor: context.onSecondaryContainer,
                  thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 10),
                  trackHeight: 10,
                ),
                child: Slider(
                  value: _currentRate,
                  min: _isWeightLoss ? 0.1 : 0.5,
                  max: _isWeightLoss ? 1.5 : 2.0,
                  onChanged: (value) {
                    // Update UI immediately for responsive feel
                    setState(() {
                      _currentRate = double.parse(value.toStringAsFixed(1));
                    });

                    // Use throttling to debounce rapid changes
                    _throttler.throttle(() {
                      HapticFeedback.selectionClick();
                      widget.onRateChanged(_currentRate);
                    });
                  },
                ),
              ),
            ),
            // Image.asset(_isWeightLoss ? 'assets/images/rabbit.png' : 'assets/images/cheetah.png', height: 24),
          ],
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(_isWeightLoss ? "0.1 ${LocaleKeys.onboarding_kg.tr()}" : "0.5 ${LocaleKeys.onboarding_kg.tr()}",
                  style: context.textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold)),
              Text(_isWeightLoss ? "0.8 ${LocaleKeys.onboarding_kg.tr()}" : "1.25 ${LocaleKeys.onboarding_kg.tr()}",
                  style: context.textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold)),
              Text(_isWeightLoss ? "1.5 ${LocaleKeys.onboarding_kg.tr()}" : "2.0 ${LocaleKeys.onboarding_kg.tr()}",
                  style: context.textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold)),
            ],
          ),
        ),
        const SizedBox(height: 24),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            _getRateMessage(),
            textAlign: TextAlign.center,
            style: context.textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.bold),
          ),
        ),
      ],
    );
  }
}
