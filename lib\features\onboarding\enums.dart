import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

enum Gender {
  male(
    'male',
    LocaleKeys.common_ma,
    Icons.male_outlined,
  ),
  female(
    'female',
    LocaleKeys.common_fe,
    Icons.female_outlined,
  );

  final String value;
  final String localizedNameKey;
  final IconData icon;

  const Gender(
    this.value,
    this.localizedNameKey,
    this.icon,
  );

  static Gender? fromValue(String? value) {
    if (value == null) return null;
    return Gender.values.firstWhere(
      (gender) => gender.value == value,
      orElse: () => Gender.male,
    );
  }
}

extension GenderExtension on Gender {
  String get localizedName {
    return localizedNameKey.tr();
  }
}

enum ExerciseFrequency {
  none(
    '0',
    "none",
    LocaleKeys.onboarding_exercise_frequency_none,
    Assets.onboardingSofa,
    1.2,
  ),
  low(
    '1-2',
    "light",
    LocaleKeys.onboarding_exercise_frequency_low,
    Assets.onboardingWalk,
    1.2,
  ),
  medium(
    '3-5',
    "moderate",
    LocaleKeys.onboarding_exercise_frequency_medium,
    Assets.onboardingDumbbell,
    1.375,
  ),
  high(
    '6+',
    "active",
    LocaleKeys.onboarding_exercise_frequency_high,
    Assets.onboardingMedal,
    1.55,
  );

  final String value;
  final String activity;
  final String localizedNameKey;
  final String icon;
  final double val;

  const ExerciseFrequency(
    this.value,
    this.activity,
    this.localizedNameKey,
    this.icon,
    this.val,
  );

  static ExerciseFrequency? fromValue(String? value) {
    if (value == null) return null;
    return ExerciseFrequency.values.firstWhere(
      (frequency) => frequency.value == value,
      orElse: () => ExerciseFrequency.medium,
    );
  }
}

enum HeardAboutUs {
  insta,
  facebook,
  tiktok,
  youtube,
  google,
  somewhereElse,
}

class HeardAboutUsData {
  final String Function() localizedName;
  final String key;
  final String icon;
  final bool doesColorChange;

  const HeardAboutUsData({
    required this.localizedName,
    required this.key,
    required this.icon,
    required this.doesColorChange,
  });
}

extension HeardAboutUsExtension on HeardAboutUs {
  static final Map<HeardAboutUs, HeardAboutUsData> _data = {
    HeardAboutUs.insta: HeardAboutUsData(
      localizedName: () => LocaleKeys.onboarding_instagram.tr(),
      key: "instagram",
      icon: Assets.onboardingInstagram,
      doesColorChange: false,
    ),
    HeardAboutUs.facebook: HeardAboutUsData(
      localizedName: () => LocaleKeys.onboarding_facebook.tr(),
      key: "facebook",
      icon: Assets.onboardingFacebook,
      doesColorChange: false,
    ),
    HeardAboutUs.tiktok: HeardAboutUsData(
      localizedName: () => LocaleKeys.onboarding_tiktok.tr(),
      key: "tiktok",
      icon: Assets.onboardingTiktok,
      doesColorChange: false,
    ),
    HeardAboutUs.youtube: HeardAboutUsData(
      localizedName: () => LocaleKeys.onboarding_youtube.tr(),
      key: "youtube",
      icon: Assets.onboardingYoutube,
      doesColorChange: false,
    ),
    HeardAboutUs.google: HeardAboutUsData(
      localizedName: () => LocaleKeys.onboarding_google.tr(),
      key: "google",
      icon: Assets.onboardingGoogle,
      doesColorChange: false,
    ),
    HeardAboutUs.somewhereElse: HeardAboutUsData(
      localizedName: () => LocaleKeys.onboarding_else.tr(),
      key: "else",
      icon: Assets.onboardingDots,
      doesColorChange: true,
    ),
  };

  String get localizedName => _data[this]!.localizedName();
  String get key => _data[this]!.key;
  String get icon => _data[this]!.icon;
  bool get doesColorChange => _data[this]!.doesColorChange;

  static HeardAboutUs? fromValue(String? value) {
    if (value == null) return null;
    return _data.entries
        .firstWhere(
          (entry) => entry.value.key == value,
          orElse: () => MapEntry(HeardAboutUs.somewhereElse, _data[HeardAboutUs.somewhereElse]!),
        )
        .key;
  }
}

enum Choice {
  no(
    'no',
    LocaleKeys.onboarding_no,
    Assets.onboardingDislike,
  ),
  yes(
    'yes',
    LocaleKeys.onboarding_yes,
    Assets.onboardingLike,
  );

  final String value;
  final String localizedNameKey;
  final String icon;

  const Choice(
    this.value,
    this.localizedNameKey,
    this.icon,
  );

  static Choice? fromValue(String? value) {
    if (value == null) return null;
    return Choice.values.firstWhere(
      (choice) => choice.value == value,
      orElse: () => Choice.yes,
    );
  }
}

extension ChoiceExtension on Choice {
  String get localizedName {
    return localizedNameKey.tr();
  }
}

enum Month {
  january,
  february,
  march,
  april,
  may,
  june,
  july,
  august,
  september,
  october,
  november,
  december,
}

extension MonthExtension on Month {
  String get localizedName {
    switch (this) {
      case Month.january:
        return LocaleKeys.common_months_january.tr();
      case Month.february:
        return LocaleKeys.common_months_february.tr();
      case Month.march:
        return LocaleKeys.common_months_march.tr();
      case Month.april:
        return LocaleKeys.common_months_april.tr();
      case Month.may:
        return LocaleKeys.common_months_may.tr();
      case Month.june:
        return LocaleKeys.common_months_june.tr();
      case Month.july:
        return LocaleKeys.common_months_july.tr();
      case Month.august:
        return LocaleKeys.common_months_august.tr();
      case Month.september:
        return LocaleKeys.common_months_september.tr();
      case Month.october:
        return LocaleKeys.common_months_october.tr();
      case Month.november:
        return LocaleKeys.common_months_november.tr();
      case Month.december:
        return LocaleKeys.common_months_december.tr();
    }
  }

  int get value {
    return index + 1;
  }

  static Month fromValue(int value) {
    return Month.values[value - 1];
  }
}

enum Goal {
  weightLoss(
    'weight_loss',
    'lose',
    LocaleKeys.onboarding_goal_weight_loss,
  ),
  maintenance(
    'maintenance',
    'maintain',
    LocaleKeys.onboarding_goal_maintenance,
  ),
  weightGain(
    'weight_gain',
    'gain',
    LocaleKeys.onboarding_goal_weight_gain,
  );

  final String value;
  final String tdeeGoal;
  final String localizedNameKey;

  const Goal(
    this.value,
    this.tdeeGoal,
    this.localizedNameKey,
  );

  static Goal? fromValue(String? value) {
    if (value == null) return null;
    return Goal.values.firstWhere(
      (goal) => goal.tdeeGoal == value,
      orElse: () => Goal.maintenance,
    );
  }
}

extension GoalExtension on Goal {
  String get localizedName {
    return localizedNameKey.tr();
  }
}

enum WhatMakesYouDoesnotCommit {
  commitLack,
  foodHabits,
  supportLack,
  busy,
  foodIdeasLack,
}

class WhatMakesYouDoesnotCommitData {
  final String Function() localizedName;
  final String icon;

  const WhatMakesYouDoesnotCommitData({
    required this.localizedName,
    required this.icon,
  });
}

extension WhatMakesYouDoesnotCommitExtension on WhatMakesYouDoesnotCommit {
  static final Map<WhatMakesYouDoesnotCommit, WhatMakesYouDoesnotCommitData> _data = {
    WhatMakesYouDoesnotCommit.commitLack: WhatMakesYouDoesnotCommitData(
      localizedName: () => LocaleKeys.onboarding_lack_of_commitment.tr(),
      icon: Assets.onboardingSolarChart,
    ),
    WhatMakesYouDoesnotCommit.foodHabits: WhatMakesYouDoesnotCommitData(
      localizedName: () => LocaleKeys.onboarding_unhealthy_eating_habits.tr(),
      icon: Assets.onboardingBurger,
    ),
    WhatMakesYouDoesnotCommit.supportLack: WhatMakesYouDoesnotCommitData(
      localizedName: () => LocaleKeys.onboarding_lack_of_support.tr(),
      icon: Assets.onboardingHandsShake,
    ),
    WhatMakesYouDoesnotCommit.busy: WhatMakesYouDoesnotCommitData(
      localizedName: () => LocaleKeys.onboarding_busy_table.tr(),
      icon: Assets.onboardingCalander,
    ),
    WhatMakesYouDoesnotCommit.foodIdeasLack: WhatMakesYouDoesnotCommitData(
      localizedName: () => LocaleKeys.onboarding_Lack_of_meal_ideas.tr(),
      icon: Assets.onboardingApple,
    ),
  };

  String get localizedName => _data[this]!.localizedName();
  String get icon => _data[this]!.icon;

  static WhatMakesYouDoesnotCommit? fromValue(String? value) {
    if (value == null) return null;
    return _data.entries
        .firstWhere(
          (entry) => entry.value.localizedName() == value,
          orElse: () => MapEntry(WhatMakesYouDoesnotCommit.commitLack, _data[WhatMakesYouDoesnotCommit.commitLack]!),
        )
        .key;
  }
}

enum Diet {
  eatEverything,
  keto,
  pescetarian,
  vegetarian,
  vegan,
}

class DietData {
  final String Function() localizedName;
  final String key;
  final String icon;

  const DietData({
    required this.localizedName,
    required this.key,
    required this.icon,
  });
}

extension DietExtension on Diet {
  static final Map<Diet, DietData> _data = {
    Diet.eatEverything: DietData(
      localizedName: () => LocaleKeys.onboarding_eat_everything.tr(),
      key: "anything",
      icon: Assets.onboardingMeat,
    ),
    Diet.keto: DietData(
      localizedName: () => LocaleKeys.onboarding_keto.tr(),
      key: "keto",
      icon: Assets.onboardingFat,
    ),
    Diet.pescetarian: DietData(
      localizedName: () => LocaleKeys.onboarding_pescetarian.tr(),
      key: "pescetarian",
      icon: Assets.onboardingFish,
    ),
    Diet.vegetarian: DietData(
      localizedName: () => LocaleKeys.onboarding_vegetarian.tr(),
      key: "vegetarian",
      icon: Assets.onboardingApple,
    ),
    Diet.vegan: DietData(
      localizedName: () => LocaleKeys.onboarding_vegan.tr(),
      key: "vegan",
      icon: Assets.onboardingPlant,
    ),
  };

  String get localizedName => _data[this]!.localizedName();
  String get key => _data[this]!.key;
  String get icon => _data[this]!.icon;

  static Diet? fromValue(String? value) {
    if (value == null) return null;
    return _data.entries
        .firstWhere(
          (entry) => entry.value.key == value,
          orElse: () => MapEntry(Diet.eatEverything, _data[Diet.eatEverything]!),
        )
        .key;
  }
}

enum Achieve {
  eatHealthyAndLiveWell,
  improveEnergyAndMood,
  stayActiveAndConsistent,
  feelSatisfiedWithBody,
}

class AchieveData {
  final String Function() localizedName;
  final String key;
  final String icon;

  const AchieveData({
    required this.localizedName,
    required this.key,
    required this.icon,
  });
}

extension AchieveExtension on Achieve {
  static final Map<Achieve, AchieveData> _data = {
    Achieve.eatHealthyAndLiveWell: AchieveData(
      localizedName: () => LocaleKeys.onboarding_eat_healthy_and_live_well.tr(),
      key: "eat healthy and live well",
      icon: Assets.onboardingApple, // Replace with appropriate icons
    ),
    Achieve.improveEnergyAndMood: AchieveData(
      localizedName: () => LocaleKeys.onboarding_improve_energy_and_mood.tr(),
      key: "improve energy and mood",
      icon: Assets.onboardingSun,
    ),
    Achieve.stayActiveAndConsistent: AchieveData(
      localizedName: () => LocaleKeys.onboarding_stay_active_and_consistent.tr(),
      key: "stay active and consistent",
      icon: Assets.onboardingMuscles,
    ),
    Achieve.feelSatisfiedWithBody: AchieveData(
      localizedName: () => LocaleKeys.onboarding_feel_satisfied_with_body.tr(),
      key: "feel satisfied with body",
      icon: Assets.onboardingYoga,
    ),
  };

  String get localizedName => _data[this]!.localizedName();
  String get key => _data[this]!.key;
  String get icon => _data[this]!.icon;

  static Achieve? fromValue(String? value) {
    if (value == null) return null;
    return _data.entries
        .firstWhere(
          (entry) => entry.value.key == value,
          orElse: () => MapEntry(Achieve.eatHealthyAndLiveWell, _data[Achieve.eatHealthyAndLiveWell]!),
        )
        .key;
  }
}
