import 'package:cal/common/widgets/custom_text_field.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Birth Date Screen
class ReferralCodeContent extends StatefulWidget {
  const ReferralCodeContent({super.key});

  @override
  State<ReferralCodeContent> createState() => _ReferralCodeContentState();
}

class _ReferralCodeContentState extends State<ReferralCodeContent> {
  final TextEditingController referralController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_do_you_have_referral_code.tr(),
          description: LocaleKeys.onboarding_you_can_skip_this_level.tr(),
          centerContent: true,
          contentWidgets: [
            CustomTextField(
              controller: referralController,
              hint: LocaleKeys.onboarding_referral_code.tr(),
              height: 60,
            )
          ],
        );
      },
    );
  }
}
