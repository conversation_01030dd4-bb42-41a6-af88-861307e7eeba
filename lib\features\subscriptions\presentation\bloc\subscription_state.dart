part of 'subscription_bloc.dart';

class SubscriptionState extends Equatable {
  final List<SubscriptionPlanEntity> subscriptionPlans;
  final SubscriptionPlanEntity? selectedPlan;
  final SubscriptionPurchaseStatus status;
  final String? errorMessage;
  final String? activeSubscriptionId;

  const SubscriptionState({
    this.subscriptionPlans = const [],
    this.selectedPlan,
    this.status = SubscriptionPurchaseStatus.initial,
    this.errorMessage,
    this.activeSubscriptionId,
  });

  bool get hasActiveSubscription => activeSubscriptionId != null;

  SubscriptionState copyWith({
    List<SubscriptionPlanEntity>? subscriptionPlans,
    SubscriptionPlanEntity? selectedPlan,
    SubscriptionPurchaseStatus? status,
    String? errorMessage,
    String? activeSubscriptionId,
  }) {
    return SubscriptionState(
      subscriptionPlans: subscriptionPlans ?? this.subscriptionPlans,
      selectedPlan: selectedPlan ?? this.selectedPlan,
      status: status ?? this.status,
      errorMessage: errorMessage,
      activeSubscriptionId: activeSubscriptionId ?? this.activeSubscriptionId,
    );
  }

  @override
  List<Object?> get props => [
        subscriptionPlans,
        selectedPlan,
        status,
        errorMessage,
        activeSubscriptionId,
      ];
}
