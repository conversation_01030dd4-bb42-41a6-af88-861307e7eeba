                        -HD:\SDK\flutter_windows_3.29.2-stable\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=D:\SDK\android-sdk\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=D:\SDK\android-sdk\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=D:\SDK\android-sdk\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\SDK\android-sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\Flutter\cal\build\app\intermediates\cxx\RelWithDebInfo\20123p1b\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\Flutter\cal\build\app\intermediates\cxx\RelWithDebInfo\20123p1b\obj\x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\Flutter\cal\android\app\.cxx\RelWithDebInfo\20123p1b\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2