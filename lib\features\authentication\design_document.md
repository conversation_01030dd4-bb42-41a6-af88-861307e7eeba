# Authentication Feature Design Document

## 1. Goal
To integrate Login with Apple and Login with Google authentication into the existing Flutter application, ensuring secure communication with a custom backend and adhering to the Clean Architecture and BloC pattern.

## 2. Architecture Overview
The authentication feature will follow the Clean Architecture principles, separating concerns into `presentation`, `domain`, and `data` layers. The BloC pattern will be used for state management within the `presentation` layer.

```
Flutter App
  ├── Presentation Layer (UI, BloC)
  │     ├── AuthenticationPage (UI)
  │     └── AuthenticationBloc (State Management)
  ├── Domain Layer (Business Logic)
  │     ├── Entities (UserEntity, AuthTokenEntity)
  │     ├── Use Cases (SignInWithAppleUseCase, SignInWithGoogleUseCase, SignOutUseCase)
  │     └── Repositories (AuthenticationRepository)
  └── Data Layer (Data Sources, Models, Repositories Implementation)
        ├── Data Sources (AuthenticationRemoteDataSource, AuthenticationLocalDataSource)
        ├── Models (UserModel, AuthTokenModel)
        └── Repositories Implementation (AuthenticationRepositoryImpl)

Backend (User's Custom Backend)
  ├── Authentication Endpoints
  │     ├── /auth/apple (Receives Apple token, verifies, returns backend token)
  │     └── /auth/google (Receives Google token, verifies, returns backend token)
  └── User Management

External Services
  ├── Apple Authentication Service
  └── Google Authentication Service
```

## 3. Data Flow

### 3.1 Login with Apple
1.  **User Action:** User taps 


the "Sign in with Apple" button.
2.  **Flutter App (Presentation Layer):** The `AuthenticationPage` triggers the `SignInWithAppleUseCase` in the `AuthenticationBloc`.
3.  **Flutter App (Domain Layer):** The `SignInWithAppleUseCase` calls the `signInWithApple` method of the `AuthenticationRepository`.
4.  **Flutter App (Data Layer):** The `AuthenticationRepositoryImpl` (implementation of `AuthenticationRepository`) calls the `signInWithApple` method of the `AuthenticationRemoteDataSource`.
5.  **Flutter App (Data Layer):** The `AuthenticationRemoteDataSource` uses the `sign_in_with_apple` Flutter package to get the Apple ID credential (specifically, the `authorizationCode`).
6.  **Flutter App (Data Layer):** The `authorizationCode` is sent to the user's backend `/auth/apple` endpoint.
7.  **Backend:** The backend receives the `authorizationCode`, verifies it with Apple's servers, and if valid, creates or retrieves a user account in its own system. The backend then generates its own authentication token (e.g., JWT) and sends it back to the Flutter app.
8.  **Flutter App (Data Layer):** The `AuthenticationRemoteDataSource` receives the backend token.
9.  **Flutter App (Data Layer):** The `AuthenticationLocalDataSource` saves the backend token securely (e.g., using `flutter_secure_storage`).
10. **Flutter App (Domain Layer):** The `SignInWithAppleUseCase` receives the success/failure status and the backend token.
11. **Flutter App (Presentation Layer):** The `AuthenticationBloc` updates its state based on the success/failure of the login, and the UI (`AuthenticationPage`) reflects this state change.

### 3.2 Login with Google
1.  **User Action:** User taps the "Sign in with Google" button.
2.  **Flutter App (Presentation Layer):** The `AuthenticationPage` triggers the `SignInWithGoogleUseCase` in the `AuthenticationBloc`.
3.  **Flutter App (Domain Layer):** The `SignInWithGoogleUseCase` calls the `signInWithGoogle` method of the `AuthenticationRepository`.
4.  **Flutter App (Data Layer):** The `AuthenticationRepositoryImpl` calls the `signInWithGoogle` method of the `AuthenticationRemoteDataSource`.
5.  **Flutter App (Data Layer):** The `AuthenticationRemoteDataSource` uses the `google_sign_in` Flutter package to get the Google ID token.
6.  **Flutter App (Data Layer):** The Google ID token is sent to the user's backend `/auth/google` endpoint.
7.  **Backend:** The backend receives the Google ID token, verifies it with Google's servers, and if valid, creates or retrieves a user account in its own system. The backend then generates its own authentication token (e.g., JWT) and sends it back to the Flutter app.
8.  **Flutter App (Data Layer):** The `AuthenticationRemoteDataSource` receives the backend token.
9.  **Flutter App (Data Layer):** The `AuthenticationLocalDataSource` saves the backend token securely.
10. **Flutter App (Domain Layer):** The `SignInWithGoogleUseCase` receives the success/failure status and the backend token.
11. **Flutter App (Presentation Layer):** The `AuthenticationBloc` updates its state based on the success/failure of the login, and the UI (`AuthenticationPage`) reflects this state change.

## 4. Backend Integration

### 4.1 Endpoints
*   `/auth/apple` (POST): Receives `authorizationCode` from Flutter app. Verifies with Apple, issues backend token.
*   `/auth/google` (POST): Receives Google `idToken` from Flutter app. Verifies with Google, issues backend token.

### 4.2 Token Management
The backend will issue its own authentication token (e.g., JWT) upon successful verification of Apple/Google credentials. This token will be used for subsequent authenticated requests from the Flutter app.

## 5. Flutter Implementation Details

### 5.1 Dependencies
Add the following to `pubspec.yaml`:
*   `sign_in_with_apple: ^latest_version`
*   `google_sign_in: ^latest_version`
*   `flutter_secure_storage: ^latest_version` (for local token storage)
*   `dio: ^latest_version` (for making HTTP requests to backend)

### 5.2 Folder Structure
Create a new `authentication` feature folder under `lib/features` with the following structure:

```
lib/features/authentication
├── data
│   ├── datasources
│   │   ├── authentication_local_datasource.dart
│   │   └── authentication_remote_datasource.dart
│   ├── models
│   │   ├── user_model.dart
│   │   └── auth_token_model.dart
│   └── repositories
│       └── authentication_repository_impl.dart
├── domain
│   ├── entities
│   │   ├── user_entity.dart
│   │   └── auth_token_entity.dart
│   ├── repositories
│   │   └── authentication_repository.dart
│   └── usecases
│       ├── sign_in_with_apple_usecase.dart
│       ├── sign_in_with_google_usecase.dart
│       └── sign_out_usecase.dart
├── presentation
│   ├── bloc
│   │   ├── authentication_bloc.dart
│   │   ├── authentication_event.dart
│   │   └── authentication_state.dart
│   └── pages
│       └── authentication_page.dart
└── di
    └── authentication_injection.dart
```

### 5.3 State Management (BloC)
*   `AuthenticationBloc`: Manages the authentication state (e.g., `unauthenticated`, `authenticating`, `authenticated`, `error`).
*   `AuthenticationEvent`: Defines events like `SignInWithApplePressed`, `SignInWithGooglePressed`, `SignOutPressed`.
*   `AuthenticationState`: Represents the current state of authentication, including `status` (enum: `initial`, `loading`, `success`, `failure`) and potentially user data or error messages. States will have `copyWith` methods for immutability.

### 5.4 Error Handling
Implement robust error handling for network requests and authentication failures, providing meaningful feedback to the user.

## 6. Next Steps
1.  Implement the backend endpoints for Apple and Google authentication.
2.  Implement the Flutter code based on the designed architecture.
3.  Test the integration thoroughly.
4.  Provide detailed documentation for both frontend and backend implementation.

