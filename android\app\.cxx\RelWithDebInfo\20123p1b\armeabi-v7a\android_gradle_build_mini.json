{"buildFiles": ["D:\\SDK\\flutter_windows_3.29.2-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\SDK\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter\\cal\\android\\app\\.cxx\\RelWithDebInfo\\20123p1b\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\SDK\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter\\cal\\android\\app\\.cxx\\RelWithDebInfo\\20123p1b\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}