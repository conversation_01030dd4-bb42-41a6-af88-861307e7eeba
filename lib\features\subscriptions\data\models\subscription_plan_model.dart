import 'package:cal/features/subscriptions/domain/entities/subscription_plan_entity.dart';
import 'package:cal/features/subscriptions/enums.dart';
import 'package:equatable/equatable.dart';

class SubscriptionPlanModel extends Equatable {
  final String id;
  final String title;
  final String description;
  final double price;
  final SubscriptionPlanType type;
  final List<String> features;

  const SubscriptionPlanModel({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.type,
    required this.features,
  });

  factory SubscriptionPlanModel.fromProductDetails(
    dynamic productDetails, 
    SubscriptionPlanType type,
    List<String> features,
  ) {
    return SubscriptionPlanModel(
      id: productDetails.id,
      title: productDetails.title,
      description: productDetails.description,
      price: productDetails.rawPrice.toDouble(),
      type: type,
      features: features,
    );
  }

  SubscriptionPlanEntity toEntity() {
    return SubscriptionPlanEntity(
      id: id,
      title: title,
      description: description,
      price: price,
      type: type,
      features: features,
    );
  }

  static List<SubscriptionPlanModel> getMockPlans() {
    return [
      const SubscriptionPlanModel(
        id: 'monthly_sub',
        title: 'Monthly Plan',
        description: 'Access to all premium features',
        price: 9.99,
        type: SubscriptionPlanType.monthly,
        features: ['Personalized workout plans', 'Nutrition tracking', 'Progress analytics'],
      ),
      const SubscriptionPlanModel(
        id: 'quarterly_sub',
        title: 'Quarterly Plan',
        description: 'Save 15% with quarterly billing',
        price: 24.99,
        type: SubscriptionPlanType.quarterly,
        features: ['Personalized workout plans', 'Nutrition tracking', 'Progress analytics', 'Premium workout videos'],
      ),
      const SubscriptionPlanModel(
        id: 'yearly_sub',
        title: 'Yearly Plan',
        description: 'Best value! Save 30% with annual billing',
        price: 79.99,
        type: SubscriptionPlanType.yearly,
        features: ['Personalized workout plans', 'Nutrition tracking', 'Progress analytics', 'Premium workout videos', 'Priority support'],
      ),
    ];
  }

  @override
  List<Object?> get props => [id, title, description, price, type, features];
}
