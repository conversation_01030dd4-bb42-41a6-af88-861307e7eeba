import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_option.dart';

import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Did Use Other Apps Screen
class DidUseOtherAppContent extends StatefulWidget {
  const DidUseOtherAppContent({super.key});

  @override
  State<DidUseOtherAppContent> createState() => _DidUseOtherAppContentState();
}

class _DidUseOtherAppContentState extends State<DidUseOtherAppContent> {
  @override
  void initState() {
    super.initState();
    context.read<OnboardingBloc>().add(RegisterScreenValidation((state) => state.hasUsedOtherApps != null));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_have_you_tried_another_app.tr(),
          contentWidgets: [
            const SizedBox(height: 85),
            ...Choice.values
                .map(
                  (choice) => OnboardingOption(
                    isSelected: state.hasUsedOtherApps == choice,
                    text: choice.localizedNameKey.tr(),
                    imagePath: choice.icon,
                    onSelected: () => context.read<OnboardingBloc>().add(UpdateHasUsedOtherApps(choice)),
                  ),
                )
                .toList(),
          ],
        );
      },
    );
  }
}
