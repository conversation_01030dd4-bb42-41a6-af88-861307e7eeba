import 'package:cal/core/di/injection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cal/features/subscriptions/presentation/bloc/subscription_bloc.dart';
import 'package:cal/features/subscriptions/presentation/pages/payment_page.dart';

class PaymentNavigationHelper {
  /// Navigate to the payment page with proper BLoC setup
  static Future<void> navigateToPaymentPage(BuildContext context) async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (context) => getIt<SubscriptionBloc>(),
          child: const PaymentPage(),
        ),
      ),
    );
  }

  /// Navigate to payment page and return result
  static Future<bool?> navigateToPaymentPageWithResult(BuildContext context) async {
    return await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (context) => getIt<SubscriptionBloc>(),
          child: const PaymentPage(),
        ),
      ),
    );
  }

  /// Show payment page as a modal bottom sheet
  static Future<void> showPaymentModal(BuildContext context) async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: BlocProvider(
          create: (context) => getIt<SubscriptionBloc>(),
          child: const PaymentPage(),
        ),
      ),
    );
  }

  /// Check if user has active subscription before showing content
  static Widget buildSubscriptionGate({
    required BuildContext context,
    required Widget child,
    Widget? paymentPrompt,
  }) {
    return BlocBuilder<SubscriptionBloc, SubscriptionState>(
      builder: (context, state) {
        if (state.hasActiveSubscription) {
          return child;
        }
        
        return paymentPrompt ?? _buildDefaultPaymentPrompt(context);
      },
    );
  }

  static Widget _buildDefaultPaymentPrompt(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.workspace_premium_outlined,
              size: 80,
              color: Colors.orange,
            ),
            const SizedBox(height: 24),
            const Text(
              'احصل على الاشتراك المميز',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'اشترك الآن للوصول إلى جميع الميزات المتقدمة',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: () => navigateToPaymentPage(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'ابدأ الاشتراك',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

