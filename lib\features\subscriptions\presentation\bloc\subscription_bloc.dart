import 'package:cal/features/subscriptions/domain/entities/subscription_plan_entity.dart';
import 'package:cal/features/subscriptions/domain/usecases/get_active_subscription_id_usecase.dart';
import 'package:cal/features/subscriptions/domain/usecases/get_subscription_plans_usecase.dart';
import 'package:cal/features/subscriptions/domain/usecases/is_store_available_usecase.dart';
import 'package:cal/features/subscriptions/domain/usecases/purchase_subscription_usecase.dart';
import 'package:cal/features/subscriptions/domain/usecases/restore_purchases_usecase.dart';
import 'package:cal/features/subscriptions/domain/usecases/verify_purchase_usecase.dart';
import 'package:cal/features/subscriptions/enums.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:in_app_purchase/in_app_purchase.dart' as iap;
import 'dart:async';

part 'subscription_event.dart';
part 'subscription_state.dart';

class SubscriptionBloc extends Bloc<SubscriptionEvent, SubscriptionState> {
  final GetSubscriptionPlansUseCase getSubscriptionPlansUseCase;
  final PurchaseSubscriptionUseCase purchaseSubscriptionUseCase;
  final RestorePurchasesUseCase restorePurchasesUseCase;
  final VerifyPurchaseUseCase verifyPurchaseUseCase;
  final GetActiveSubscriptionIdUseCase getActiveSubscriptionIdUseCase;
  final IsStoreAvailableUseCase isStoreAvailableUseCase;

  StreamSubscription? _purchaseUpdateSubscription;

  SubscriptionBloc({
    required this.getSubscriptionPlansUseCase,
    required this.purchaseSubscriptionUseCase,
    required this.restorePurchasesUseCase,
    required this.verifyPurchaseUseCase,
    required this.getActiveSubscriptionIdUseCase,
    required this.isStoreAvailableUseCase,
  }) : super(const SubscriptionState()) {
    on<InitializeSubscriptions>(_initializeSubscriptions);
    on<LoadSubscriptionPlans>(_loadSubscriptionPlans);
    on<SelectSubscriptionPlan>(_selectSubscriptionPlan);
    on<PurchaseSubscription>(_purchaseSubscription);
    on<UpdatePurchaseStatus>(_updatePurchaseStatus);
    on<RestorePurchases>(_restorePurchases);
    on<VerifyPurchase>(_verifyPurchase);
    on<HandlePurchaseUpdate>(_handlePurchaseUpdate);
  }

  Future<void> _initializeSubscriptions(
    InitializeSubscriptions event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionPurchaseStatus.loading));

    // Check if store is available
    final storeAvailableResult = await isStoreAvailableUseCase();

    final bool storeAvailable = storeAvailableResult.fold(
      (error) {
        emit(state.copyWith(
          status: SubscriptionPurchaseStatus.error,
          errorMessage: error,
        ));
        return false;
      },
      (isAvailable) => isAvailable,
    );

    if (!storeAvailable) {
      emit(state.copyWith(
        status: SubscriptionPurchaseStatus.error,
        errorMessage: 'Store is not available',
      ));
      return;
    }

    // Get active subscription ID
    final activeSubscriptionResult = await getActiveSubscriptionIdUseCase();

    activeSubscriptionResult.fold(
      (error) {
        emit(state.copyWith(
          status: SubscriptionPurchaseStatus.error,
          errorMessage: error,
        ));
      },
      (activeSubscriptionId) {
        emit(state.copyWith(
          status: SubscriptionPurchaseStatus.initial,
          activeSubscriptionId: activeSubscriptionId,
        ));
      },
    );

    // Load subscription plans
    add(const LoadSubscriptionPlans());
  }

  Future<void> _loadSubscriptionPlans(
    LoadSubscriptionPlans event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionPurchaseStatus.loading));

    final plansResult = await getSubscriptionPlansUseCase();

    plansResult.fold(
      (error) {
        emit(state.copyWith(
          status: SubscriptionPurchaseStatus.error,
          errorMessage: error,
        ));
      },
      (plans) {
        emit(state.copyWith(
          status: SubscriptionPurchaseStatus.initial,
          subscriptionPlans: plans,
        ));
      },
    );
  }

  void _selectSubscriptionPlan(
    SelectSubscriptionPlan event,
    Emitter<SubscriptionState> emit,
  ) {
    emit(state.copyWith(selectedPlan: event.plan));
  }

  Future<void> _purchaseSubscription(
    PurchaseSubscription event,
    Emitter<SubscriptionState> emit,
  ) async {
    if (state.selectedPlan == null) {
      emit(state.copyWith(
        status: SubscriptionPurchaseStatus.error,
        errorMessage: 'No subscription plan selected',
      ));
      return;
    }

    emit(state.copyWith(status: SubscriptionPurchaseStatus.loading));

    final purchaseResult = await purchaseSubscriptionUseCase(state.selectedPlan!);

    purchaseResult.fold(
      (error) {
        emit(state.copyWith(
          status: SubscriptionPurchaseStatus.error,
          errorMessage: error,
        ));
      },
      (_) {
        // Purchase initiated, result will be handled via purchase updates
      },
    );
  }

  void _handlePurchaseUpdate(
    HandlePurchaseUpdate event,
    Emitter<SubscriptionState> emit,
  ) {
    final purchaseDetails = event.purchaseDetails;

    if (purchaseDetails.status == iap.PurchaseStatus.pending) {
      add(const UpdatePurchaseStatus(status: SubscriptionPurchaseStatus.loading));
    } else if (purchaseDetails.status == iap.PurchaseStatus.purchased || purchaseDetails.status == iap.PurchaseStatus.restored) {
      add(VerifyPurchase(purchaseDetails: purchaseDetails));
    } else if (purchaseDetails.status == iap.PurchaseStatus.error) {
      add(UpdatePurchaseStatus(
        status: SubscriptionPurchaseStatus.error,
        errorMessage: purchaseDetails.error?.message ?? 'An unknown error occurred',
      ));
    }
  }

  void _updatePurchaseStatus(
    UpdatePurchaseStatus event,
    Emitter<SubscriptionState> emit,
  ) {
    emit(state.copyWith(
      status: event.status,
      errorMessage: event.errorMessage,
    ));
  }

  Future<void> _restorePurchases(
    RestorePurchases event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionPurchaseStatus.loading));

    final restoreResult = await restorePurchasesUseCase();

    restoreResult.fold(
      (error) {
        emit(state.copyWith(
          status: SubscriptionPurchaseStatus.error,
          errorMessage: error,
        ));
      },
      (_) {
        // Restore initiated, results will be delivered via purchase updates
      },
    );
  }

  Future<void> _verifyPurchase(
    VerifyPurchase event,
    Emitter<SubscriptionState> emit,
  ) async {
    final verifyResult = await verifyPurchaseUseCase(event.purchaseDetails);

    verifyResult.fold(
      (error) {
        emit(state.copyWith(
          status: SubscriptionPurchaseStatus.error,
          errorMessage: error,
        ));
      },
      (productId) {
        emit(state.copyWith(
          status: SubscriptionPurchaseStatus.purchased,
          activeSubscriptionId: productId,
        ));
      },
    );
  }

  @override
  Future<void> close() {
    _purchaseUpdateSubscription?.cancel();
    return super.close();
  }
}
