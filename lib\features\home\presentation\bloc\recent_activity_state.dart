part of 'recent_activity_bloc.dart';

enum RecentActivityStatus { initial, loading, success, failure }

class RecentActivityState extends Equatable {
  final RecentActivityStatus status;
  final List<FoodModel> foodList;
  final List<ExerciseModel> exerciseList;
  final String? errorMessage;

  const RecentActivityState({
    this.status = RecentActivityStatus.initial,
    this.foodList = const [],
    this.exerciseList = const [],
    this.errorMessage,
  });

  RecentActivityState copyWith({
    RecentActivityStatus? status,
    List<FoodModel>? foodList,
    List<ExerciseModel>? exerciseList,
    String? errorMessage,
  }) {
    return RecentActivityState(
      status: status ?? this.status,
      foodList: foodList ?? this.foodList,
      exerciseList: exerciseList ?? this.exerciseList,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, foodList, exerciseList, errorMessage];
}
