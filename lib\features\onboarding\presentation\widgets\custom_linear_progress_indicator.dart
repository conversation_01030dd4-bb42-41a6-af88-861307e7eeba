import 'package:cal/common/extentions/colors_extension.dart';
import 'package:flutter/material.dart';

class CustomLinearProgressIndicator extends StatelessWidget {
  final Color? color;
  final double? minHeight;
  final double? value;
  const CustomLinearProgressIndicator({super.key, this.color, this.minHeight, this.value});

  @override
  Widget build(BuildContext context) {
    return LinearProgressIndicator(
      borderRadius: BorderRadius.circular(40),
      value: value ?? 0.8,
      backgroundColor: Colors.grey[300],
      minHeight: minHeight ?? 2,
      color: color ?? context.primaryColor,
    );
  }
}
