import 'package:cal/core/config/endpoints.dart';
import 'package:cal/core/network/api_handler.dart';
import 'package:cal/core/network/exceptions.dart';
import 'package:cal/core/network/http_client.dart';
import 'package:cal/features/onboarding/data/models/onboarding_model.dart';
import 'package:cal/features/onboarding/domain/usecases/submit_onboarding_usecase.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class OnboardingRemoteDatasource with ApiHandler {
  final HTTPClient httpClient;

  OnboardingRemoteDatasource({required this.httpClient});

  Future<Either<Failure, void>> submitOnboarding({required SubmitOnboardingParams submitOnboardingParams}) {
    return handleApiCall(
      apiCall: () => httpClient.post(
        AppEndPoint.userOnabarding,
        data: submitOnboardingParams.getBody(),
      ),
      fromJson: (json) => OnboardingModel.fromJson(json),
    );
  }
}
