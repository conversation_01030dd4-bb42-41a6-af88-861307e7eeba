import 'dart:ui';

import 'package:cal/common/theme/app_theme.dart';
import 'package:cal/features/main/presentation/pages/splash_screen.dart';

import 'package:catcher_2/core/catcher_2.dart';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
      child: MaterialApp(
        scrollBehavior: const MaterialScrollBehavior().copyWith(
          dragDevices: {
            PointerDeviceKind.mouse,
            PointerDeviceKind.touch,
            PointerDeviceKind.stylus,
            PointerDeviceKind.unknown,
          },
        ),
        theme: AppTheme.lightTheme,
        navigatorKey: Catcher2.navigator<PERSON>ey,
        debugShowCheckedModeBanner: false,
        localizationsDelegates: context.localizationDelegates,
        supportedLocales: context.supportedLocales,
        locale: context.locale,
        home: splashScrenn(),
      ),
    );
  }
}
