// Domain Entity for User
class UserEntity {
  final String id;
  final String email;
  final String? name;
  final String? profilePictureUrl;
  final AuthProvider authProvider;

  const UserEntity({
    required this.id,
    required this.email,
    this.name,
    this.profilePictureUrl,
    required this.authProvider,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserEntity &&
        other.id == id &&
        other.email == email &&
        other.name == name &&
        other.profilePictureUrl == profilePictureUrl &&
        other.authProvider == authProvider;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        email.hashCode ^
        name.hashCode ^
        profilePictureUrl.hashCode ^
        authProvider.hashCode;
  }

  @override
  String toString() {
    return 'UserEntity(id: $id, email: $email, name: $name, profilePictureUrl: $profilePictureUrl, authProvider: $authProvider)';
  }
}

enum AuthProvider {
  apple,
  google,
}

