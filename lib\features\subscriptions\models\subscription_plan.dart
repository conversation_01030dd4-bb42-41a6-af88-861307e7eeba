import 'package:equatable/equatable.dart';
import 'package:cal/features/subscriptions/enums.dart';

class SubscriptionPlan extends Equatable {
  final String id;
  final String title;
  final String description;
  final double price;
  final SubscriptionPlanType type;
  final List<String> features;

  const SubscriptionPlan({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.type,
    required this.features,
  });

  @override
  List<Object?> get props => [id, title, description, price, type, features];

  SubscriptionPlan copyWith({
    String? id,
    String? title,
    String? description,
    double? price,
    SubscriptionPlanType? type,
    List<String>? features,
  }) {
    return SubscriptionPlan(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      type: type ?? this.type,
      features: features ?? this.features,
    );
  }
}
