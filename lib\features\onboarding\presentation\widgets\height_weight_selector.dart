import 'package:cal/common/extentions/colors_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:throttling/throttling.dart';


class HeightSelector extends StatefulWidget {
  final int initialHeight;
  final ValueChanged<int> onHeightChanged;

  const HeightSelector({
    super.key,
    required this.initialHeight,
    required this.onHeightChanged,
  });

  @override
  State<HeightSelector> createState() => _HeightSelectorState();
}

class _HeightSelectorState extends State<HeightSelector> {
  late int selectedHeight;
  late FixedExtentScrollController controller;
  late Throttling throttler;

  @override
  void initState() {
    super.initState();
    selectedHeight = widget.initialHeight;
    controller = FixedExtentScrollController(initialItem: selectedHeight - 120);
    throttler = Throttling(duration: const Duration(milliseconds: 300));
  }

  @override
  void dispose() {
    controller.dispose();
    throttler.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 200,
      child: ListWheelScrollView.useDelegate(
        itemExtent: 40,
        diameterRatio: 1.5,
        physics: const FixedExtentScrollPhysics(),
        onSelectedItemChanged: (index) {
          HapticFeedback.selectionClick();
          final height = index + 70;
          setState(() {
            selectedHeight = height;
          });

          // Use throttling to debounce rapid changes
          throttler.throttle(() {
            widget.onHeightChanged(height);
          });
        },
        controller: controller,
        childDelegate: ListWheelChildBuilderDelegate(
          childCount: 231,
          builder: (context, index) {
            final height = index + 70;
            final isSelected = height == selectedHeight;
            return Center(
              child: Text(
                '$height CM',
                style: context.textTheme.bodyMedium!.copyWith(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? context.primaryColor : Colors.black,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class WeightSelector extends StatefulWidget {
  final int initialWeight;
  final ValueChanged<int> onWeightChanged;

  const WeightSelector({
    super.key,
    required this.initialWeight,
    required this.onWeightChanged,
  });

  @override
  State<WeightSelector> createState() => _WeightSelectorState();
}

class _WeightSelectorState extends State<WeightSelector> {
  late int selectedWeight;
  late FixedExtentScrollController controller;
  late Throttling throttler;

  @override
  void initState() {
    super.initState();
    selectedWeight = widget.initialWeight;
    controller = FixedExtentScrollController(initialItem: selectedWeight - 30);
    throttler = Throttling(duration: const Duration(milliseconds: 300));
  }

  @override
  void dispose() {
    controller.dispose();
    throttler.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 200,
      child: ListWheelScrollView.useDelegate(
        itemExtent: 40,
        diameterRatio: 1.5,
        physics: const FixedExtentScrollPhysics(),
        onSelectedItemChanged: (index) {
          HapticFeedback.selectionClick();
          final weight = index + 30;
          setState(() {
            selectedWeight = weight;
          });
          throttler.throttle(() {
            widget.onWeightChanged(weight);
          });
        },
        controller: controller,
        childDelegate: ListWheelChildBuilderDelegate(
          childCount: 371, // 30 to 200 kg
          builder: (context, index) {
            final weight = index + 30;
            final isSelected = weight == selectedWeight;
            return Center(
              child: Text(
                '$weight KG',
                style: context.textTheme.bodyMedium!.copyWith(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? context.primaryColor : Colors.black,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
