import 'package:cal/features/subscriptions/domain/repositories/subscription_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:in_app_purchase/in_app_purchase.dart' as iap;

class VerifyPurchaseUseCase {
  final SubscriptionRepository repository;

  VerifyPurchaseUseCase(this.repository);

  Future<Either<String, String>> call(iap.PurchaseDetails purchaseDetails) async {
    return await repository.verifyPurchase(purchaseDetails);
  }
}
