import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class IngredientCard extends StatelessWidget {
  const IngredientCard({super.key, required this.image, required this.item, this.value = 0});

  final String image;
  final String item;
  final double? value;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: context.onSecondary.withAlpha(29),
          width: 1,
        ),
        color: context.onPrimaryColor,
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsetsDirectional.symmetric(horizontal: 20, vertical: 10),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircleAvatar(
                backgroundColor: context.background,
                radius: 10,
                child: AppImage.asset(image),
              ),
              const SizedBox(width: 5),
              Flexible(
                child: AppText(
                  item,
                  scrollText: true,
                  maxLines: 1,
                  style: context.textTheme.labelMedium!.copyWith(
                    color: context.onSecondary,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 5),
          AppText.bodyMedium(
            "${value?.toStringAsFixed(2)} ${LocaleKeys.home_gram.tr()}",
            color: context.primaryColor,
            fontWeight: FontWeight.bold,
          )
        ],
      ),
    );
  }
}
