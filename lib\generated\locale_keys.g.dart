// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: constant_identifier_names

abstract class  LocaleKeys {
  static const common_ma = 'common.ma';
  static const common_fe = 'common.fe';
  static const common_months_january = 'common.months.january';
  static const common_months_february = 'common.months.february';
  static const common_months_march = 'common.months.march';
  static const common_months_april = 'common.months.april';
  static const common_months_may = 'common.months.may';
  static const common_months_june = 'common.months.june';
  static const common_months_july = 'common.months.july';
  static const common_months_august = 'common.months.august';
  static const common_months_september = 'common.months.september';
  static const common_months_october = 'common.months.october';
  static const common_months_november = 'common.months.november';
  static const common_months_december = 'common.months.december';
  static const common_months = 'common.months';
  static const common_select_language = 'common.select_language';
  static const common_navbar_home = 'common.navbar.home';
  static const common_navbar_settings = 'common.navbar.settings';
  static const common_navbar_profile = 'common.navbar.profile';
  static const common_navbar_add = 'common.navbar.add';
  static const common_navbar = 'common.navbar';
  static const common_calories = 'common.calories';
  static const common_carbs = 'common.carbs';
  static const common_protien = 'common.protien';
  static const common_fat = 'common.fat';
  static const common_servings = 'common.servings';
  static const common_halal = 'common.halal';
  static const common_haram = 'common.haram';
  static const common_health_score = 'common.health_score';
  static const common_add = 'common.add';
  static const common_no_internet_connection = 'common.no_internet_connection';
  static const common_back = 'common.back';
  static const common_hour = 'common.hour';
  static const common_minute = 'common.minute';
  static const common_do_u_want_to_logout = 'common.do_u_want_to_logout';
  static const common_confirm = 'common.confirm';
  static const common_cancel = 'common.cancel';
  static const common_continue = 'common.continue';
  static const common_error_recognizing_food = 'common.error_recognizing_food';
  static const common = 'common';
  static const onboarding_your_weight = 'onboarding.your_weight';
  static const onboarding_gram = 'onboarding.gram';
  static const onboarding_lets_start = 'onboarding.lets_start';
  static const onboarding_what_is_your_goal = 'onboarding.what_is_your_goal';
  static const onboarding_goal_weight_loss = 'onboarding.goal_weight_loss';
  static const onboarding_goal_maintenance = 'onboarding.goal_maintenance';
  static const onboarding_goal_weight_gain = 'onboarding.goal_weight_gain';
  static const onboarding_one_photo = 'onboarding.one_photo';
  static const onboarding_one_photo_desc = 'onboarding.one_photo_desc';
  static const onboarding_halal = 'onboarding.halal';
  static const onboarding_halal_desc = 'onboarding.halal_desc';
  static const onboarding_health = 'onboarding.health';
  static const onboarding_health_desc = 'onboarding.health_desc';
  static const onboarding_start = 'onboarding.start';
  static const onboarding_next = 'onboarding.next';
  static const onboarding_choose_your_gender = 'onboarding.choose_your_gender';
  static const onboarding_we_will_use_this_information_to_personalize_your_plan = 'onboarding.we_will_use_this_information_to_personalize_your_plan';
  static const onboarding_exercise_frequency = 'onboarding.exercise_frequency';
  static const onboarding_exercise_frequency_none = 'onboarding.exercise_frequency_none';
  static const onboarding_exercise_frequency_low = 'onboarding.exercise_frequency_low';
  static const onboarding_exercise_frequency_medium = 'onboarding.exercise_frequency_medium';
  static const onboarding_exercise_frequency_high = 'onboarding.exercise_frequency_high';
  static const onboarding_height = 'onboarding.height';
  static const onboarding_weight = 'onboarding.weight';
  static const onboarding_have_you_tried_another_app = 'onboarding.have_you_tried_another_app';
  static const onboarding_yes = 'onboarding.yes';
  static const onboarding_no = 'onboarding.no';
  static const onboarding_orange_ai_achieves_long_term_results = 'onboarding.orange_ai_achieves_long_term_results';
  static const onboarding_users_maintain_weight_loss_even_after_6_months = 'onboarding.users_maintain_weight_loss_even_after_6_months';
  static const onboarding_with_orange_ai_youll_lose_more_weight_than_with_regular_methods = 'onboarding.with_orange_ai_youll_lose_more_weight_than_with_regular_methods';
  static const onboarding_orange = 'onboarding.orange';
  static const onboarding_with = 'onboarding.with';
  static const onboarding_without = 'onboarding.without';
  static const onboarding_orange_make_it_easy = 'onboarding.orange_make_it_easy';
  static const onboarding_height_and_weight = 'onboarding.height_and_weight';
  static const onboarding_birthday = 'onboarding.birthday';
  static const onboarding_month = 'onboarding.month';
  static const onboarding_day = 'onboarding.day';
  static const onboarding_year = 'onboarding.year';
  static const onboarding_please_select_gender = 'onboarding.please_select_gender';
  static const onboarding_please_select_exercise_frequency = 'onboarding.please_select_exercise_frequency';
  static const onboarding_please_select_an_option = 'onboarding.please_select_an_option';
  static const onboarding_please_confirm_height_and_weight = 'onboarding.please_confirm_height_and_weight';
  static const onboarding_please_enter_valid_birth_date = 'onboarding.please_enter_valid_birth_date';
  static const onboarding_target_weight_loss = 'onboarding.target_weight_loss';
  static const onboarding_target_weight_gain = 'onboarding.target_weight_gain';
  static const onboarding_target_weight_loss_description = 'onboarding.target_weight_loss_description';
  static const onboarding_target_weight_gain_description = 'onboarding.target_weight_gain_description';
  static const onboarding_target_weight_value = 'onboarding.target_weight_value';
  static const onboarding_kg = 'onboarding.kg';
  static const onboarding_current = 'onboarding.current';
  static const onboarding_loss = 'onboarding.loss';
  static const onboarding_gain = 'onboarding.gain';
  static const onboarding_realistic_goal = 'onboarding.realistic_goal';
  static const onboarding_onboarding_weight_loss_goal_subtitle = 'onboarding.onboarding_weight_loss_goal_subtitle';
  static const onboarding_onboarding_weight_gain_goal_subtitle = 'onboarding.onboarding_weight_gain_goal_subtitle';
  static const onboarding_onboarding_goal_statistics = 'onboarding.onboarding_goal_statistics';
  static const onboarding_onboarding_kg = 'onboarding.onboarding_kg';
  static const onboarding_onboarding_target_weight_value = 'onboarding.onboarding_target_weight_value';
  static const onboarding_onboarding_current = 'onboarding.onboarding_current';
  static const onboarding_weekly_weight_loss_rate = 'onboarding.weekly_weight_loss_rate';
  static const onboarding_weekly_weight_gain_rate = 'onboarding.weekly_weight_gain_rate';
  static const onboarding_weight_loss_speed = 'onboarding.weight_loss_speed';
  static const onboarding_weight_gain_speed = 'onboarding.weight_gain_speed';
  static const onboarding_ideal_rate = 'onboarding.ideal_rate';
  static const onboarding_slow = 'onboarding.slow';
  static const onboarding_fast = 'onboarding.fast';
  static const onboarding_days = 'onboarding.days';
  static const onboarding_dependeng_on_the_informaions = 'onboarding.dependeng_on_the_informaions';
  static const onboarding_Weight_loss_in_the_first_seven_days_is_slow_but_after_that_you_can_burn_fat_quickly = 'onboarding.Weight_loss_in_the_first_seven_days_is_slow_but_after_that_you_can_burn_fat_quickly';
  static const onboarding_youre_more_likely_to_achieve_your_goal = 'onboarding.youre_more_likely_to_achieve_your_goal';
  static const onboarding_target_calories = 'onboarding.target_calories';
  static const onboarding_target_carbs = 'onboarding.target_carbs';
  static const onboarding_target_fats = 'onboarding.target_fats';
  static const onboarding_target_proteins = 'onboarding.target_proteins';
  static const onboarding_manual_input = 'onboarding.manual_input';
  static const onboarding_instagram = 'onboarding.instagram';
  static const onboarding_facebook = 'onboarding.facebook';
  static const onboarding_tiktok = 'onboarding.tiktok';
  static const onboarding_youtube = 'onboarding.youtube';
  static const onboarding_google = 'onboarding.google';
  static const onboarding_else = 'onboarding.else';
  static const onboarding_welcome = 'onboarding.welcome';
  static const onboarding_welcome_desc = 'onboarding.welcome_desc';
  static const onboarding_welcome_second_desc = 'onboarding.welcome_second_desc';
  static const onboarding_target = 'onboarding.target';
  static const onboarding_where_did_you_hear_of_us = 'onboarding.where_did_you_hear_of_us';
  static const onboarding_set_up_everything_for_you = 'onboarding.set_up_everything_for_you';
  static const onboarding_finishing_results = 'onboarding.finishing_results';
  static const onboarding_daily_recommendations_for = 'onboarding.daily_recommendations_for';
  static const onboarding_health_result = 'onboarding.health_result';
  static const onboarding_calories = 'onboarding.calories';
  static const onboarding_carbs = 'onboarding.carbs';
  static const onboarding_protien = 'onboarding.protien';
  static const onboarding_fat = 'onboarding.fat';
  static const onboarding_Your_goal_is_to_lose_weight = 'onboarding.Your_goal_is_to_lose_weight';
  static const onboarding_Your_goal_is_to_maintain = 'onboarding.Your_goal_is_to_maintain';
  static const onboarding_Your_goal_is_to_gain_weight = 'onboarding.Your_goal_is_to_gain_weight';
  static const onboarding_kg_by = 'onboarding.kg_by';
  static const onboarding_daily_recommendations = 'onboarding.daily_recommendations';
  static const onboarding_what_doesnot_make_you_commit = 'onboarding.what_doesnot_make_you_commit';
  static const onboarding_lack_of_commitment = 'onboarding.lack_of_commitment';
  static const onboarding_unhealthy_eating_habits = 'onboarding.unhealthy_eating_habits';
  static const onboarding_lack_of_support = 'onboarding.lack_of_support';
  static const onboarding_busy_table = 'onboarding.busy_table';
  static const onboarding_Lack_of_meal_ideas = 'onboarding.Lack_of_meal_ideas';
  static const onboarding_you_can_edit_anytime = 'onboarding.you_can_edit_anytime';
  static const onboarding_what_is_your_diet = 'onboarding.what_is_your_diet';
  static const onboarding_eat_everything = 'onboarding.eat_everything';
  static const onboarding_keto = 'onboarding.keto';
  static const onboarding_pescetarian = 'onboarding.pescetarian';
  static const onboarding_vegetarian = 'onboarding.vegetarian';
  static const onboarding_vegan = 'onboarding.vegan';
  static const onboarding_meal_one = 'onboarding.meal_one';
  static const onboarding_meal_two = 'onboarding.meal_two';
  static const onboarding_meal_three = 'onboarding.meal_three';
  static const onboarding_waht_you_want_to_achieve = 'onboarding.waht_you_want_to_achieve';
  static const onboarding_eat_healthy_and_live_well = 'onboarding.eat_healthy_and_live_well';
  static const onboarding_improve_energy_and_mood = 'onboarding.improve_energy_and_mood';
  static const onboarding_stay_active_and_consistent = 'onboarding.stay_active_and_consistent';
  static const onboarding_feel_satisfied_with_body = 'onboarding.feel_satisfied_with_body';
  static const onboarding_do_you_eat_all_meals_same_time = 'onboarding.do_you_eat_all_meals_same_time';
  static const onboarding_choose_meal_time = 'onboarding.choose_meal_time';
  static const onboarding_submit = 'onboarding.submit';
  static const onboarding_cancel = 'onboarding.cancel';
  static const onboarding_share_your_opinion = 'onboarding.share_your_opinion';
  static const onboarding_How_has_your_experience_with_us_been_so_far = 'onboarding.How_has_your_experience_with_us_been_so_far';
  static const onboarding_Reach_your_goal_and_activate_notifications = 'onboarding.Reach_your_goal_and_activate_notifications';
  static const onboarding_You_can_turn_off_any_of_the_notifications_at_any_time = 'onboarding.You_can_turn_off_any_of_the_notifications_at_any_time';
  static const onboarding_Start_your_3day_free_trial = 'onboarding.Start_your_3day_free_trial';
  static const onboarding_today = 'onboarding.today';
  static const onboarding_yesterday = 'onboarding.yesterday';
  static const onboarding_Unlock_all_the_apps_features = 'onboarding.Unlock_all_the_apps_features';
  static const onboarding_after_two_days_reminder = 'onboarding.after_two_days_reminder';
  static const onboarding_Well_send_you_a_reminder = 'onboarding.Well_send_you_a_reminder';
  static const onboarding_after_3_days_Start_Subscription = 'onboarding.after_3_days_Start_Subscription';
  static const onboarding_You_will_be_charged_on_May_28_2025 = 'onboarding.You_will_be_charged_on_May_28_2025';
  static const onboarding_do_you_have_referral_code = 'onboarding.do_you_have_referral_code';
  static const onboarding_your_weight_changes = 'onboarding.your_weight_changes';
  static const onboarding_you_can_skip_this_level = 'onboarding.you_can_skip_this_level';
  static const onboarding_referral_code = 'onboarding.referral_code';
  static const onboarding_left_cals = 'onboarding.left_cals';
  static const onboarding_Add_up_to_200_calories_from_yesterday_to_todays_daily_goal = 'onboarding.Add_up_to_200_calories_from_yesterday_to_todays_daily_goal';
  static const onboarding_add_burned_calories_again = 'onboarding.add_burned_calories_again';
  static const onboarding_daily_target = 'onboarding.daily_target';
  static const onboarding_run = 'onboarding.run';
  static const onboarding_cal = 'onboarding.cal';
  static const onboarding_calorie = 'onboarding.calorie';
  static const onboarding_connect_to = 'onboarding.connect_to';
  static const onboarding_skip = 'onboarding.skip';
  static const onboarding_sync_your_daily_activity_between = 'onboarding.sync_your_daily_activity_between';
  static const onboarding_and_Apple_Health_for_more_comprehensive_data = 'onboarding.and_Apple_Health_for_more_comprehensive_data';
  static const onboarding = 'onboarding';
  static const home_settings = 'home.settings';
  static const home_progress = 'home.progress';
  static const home_home = 'home.home';
  static const home_add = 'home.add';
  static const home_remaining_carbs = 'home.remaining_carbs';
  static const home_remaining_proteins = 'home.remaining_proteins';
  static const home_remaining_fats = 'home.remaining_fats';
  static const home_remaining_cals = 'home.remaining_cals';
  static const home_burned_calories = 'home.burned_calories';
  static const home_recently_added = 'home.recently_added';
  static const home_gram = 'home.gram';
  static const home_calorie = 'home.calorie';
  static const home_delete = 'home.delete';
  static const home_unknown = 'home.unknown';
  static const home_analyzing = 'home.analyzing';
  static const home_calculating_nutrition = 'home.calculating_nutrition';
  static const home_processing_image = 'home.processing_image';
  static const home_identifying_food = 'home.identifying_food';
  static const home_computing_calories = 'home.computing_calories';
  static const home = 'home';
  static const food_database_food_database = 'food_database.food_database';
  static const food_database_no_food = 'food_database.no_food';
  static const food_database_describe_what_you_ate = 'food_database.describe_what_you_ate';
  static const food_database_all = 'food_database.all';
  static const food_database_my_meals = 'food_database.my_meals';
  static const food_database_my_food = 'food_database.my_food';
  static const food_database_favorite_meals = 'food_database.favorite_meals';
  static const food_database_enter_your_meals = 'food_database.enter_your_meals';
  static const food_database_entrance_recently = 'food_database.entrance_recently';
  static const food_database_create_meal = 'food_database.create_meal';
  static const food_database_enter_name = 'food_database.enter_name';
  static const food_database_meal_nutarians = 'food_database.meal_nutarians';
  static const food_database_add_nutrians_to_this_meal = 'food_database.add_nutrians_to_this_meal';
  static const food_database_save = 'food_database.save';
  static const food_database = 'food_database';
  static const progress_title = 'progress.title';
  static const progress_this_week = 'progress.this_week';
  static const progress_last_week = 'progress.last_week';
  static const progress_two_weeks_ago = 'progress.two_weeks_ago';
  static const progress_three_weeks_ago = 'progress.three_weeks_ago';
  static const progress_target_progress = 'progress.target_progress';
  static const progress_carbs = 'progress.carbs';
  static const progress_fats = 'progress.fats';
  static const progress_protein = 'progress.protein';
  static const progress_bmi = 'progress.bmi';
  static const progress_underweight = 'progress.underweight';
  static const progress_healthy = 'progress.healthy';
  static const progress_overweight = 'progress.overweight';
  static const progress_obese = 'progress.obese';
  static const progress_scale_time = 'progress.scale_time';
  static const progress_days = 'progress.days';
  static const progress_enter_weight = 'progress.enter_weight';
  static const progress_registration_days = 'progress.registration_days';
  static const progress_months = 'progress.months';
  static const progress_year = 'progress.year';
  static const progress_all = 'progress.all';
  static const progress_your_current_weight_is = 'progress.your_current_weight_is';
  static const progress_cals_sum = 'progress.cals_sum';
  static const progress = 'progress';
  static const settings_settings = 'settings.settings';
  static const settings_personal_details = 'settings.personal_details';
  static const settings_customize_food_items = 'settings.customize_food_items';
  static const settings_nutrition_and_weight = 'settings.nutrition_and_weight';
  static const settings_weight_log = 'settings.weight_log';
  static const settings_new = 'settings.new';
  static const settings_language = 'settings.language';
  static const settings_Preferences = 'settings.Preferences';
  static const settings_Add_the_calories_burned = 'settings.Add_the_calories_burned';
  static const settings_Add_the_calories_burned_back_to_the_daily_goal = 'settings.Add_the_calories_burned_back_to_the_daily_goal';
  static const settings_about_calories = 'settings.about_calories';
  static const settings_Add_up_to_200_calories_from_yesterday_to_todays_daily_goal = 'settings.Add_up_to_200_calories_from_yesterday_to_todays_daily_goal';
  static const settings_Disable_nutrien_recalculation = 'settings.Disable_nutrien_recalculation';
  static const settings_Enable_this_option_to_manually_specify_calories_and_nutrients_when_editing_food = 'settings.Enable_this_option_to_manually_specify_calories_and_nutrients_when_editing_food';
  static const settings_Terms_and_conditions = 'settings.Terms_and_conditions';
  static const settings_Privacy_Policy = 'settings.Privacy_Policy';
  static const settings_delete_account = 'settings.delete_account';
  static const settings_log_out = 'settings.log_out';
  static const settings = 'settings';
  static const quick_actions_scan_food = 'quick_actions.scan_food';
  static const quick_actions_add_exercise = 'quick_actions.add_exercise';
  static const quick_actions_food_database = 'quick_actions.food_database';
  static const quick_actions_saved_food = 'quick_actions.saved_food';
  static const quick_actions_select_language = 'quick_actions.select_language';
  static const quick_actions = 'quick_actions';
  static const navbar_home = 'navbar.home';
  static const navbar_settings = 'navbar.settings';
  static const navbar_progress = 'navbar.progress';
  static const navbar_add = 'navbar.add';
  static const navbar = 'navbar';
  static const auth_complete_account = 'auth.complete_account';
  static const auth_login_to_save_ur_data = 'auth.login_to_save_ur_data';
  static const auth_login_with_google = 'auth.login_with_google';
  static const auth_login_with_apple = 'auth.login_with_apple';
  static const auth = 'auth';
  static const scan_gallery = 'scan.gallery';
  static const scan_scan_food = 'scan.scan_food';
  static const scan_retry = 'scan.retry';
  static const scan = 'scan';
  static const payment_restore = 'payment.restore';
  static const payment_free_trial_start_message = 'payment.free_trial_start_message';
  static const payment_no_payment_due = 'payment.no_payment_due';
  static const payment_free_for_3_days = 'payment.free_for_3_days';
  static const payment_choose_subscription_plan = 'payment.choose_subscription_plan';
  static const payment_start_3_days_free = 'payment.start_3_days_free';
  static const payment_try_for_free = 'payment.try_for_free';
  static const payment_yearly_price_info = 'payment.yearly_price_info';
  static const payment_one_time_offer = 'payment.one_time_offer';
  static const payment_never_see_again = 'payment.never_see_again';
  static const payment_discount_message = 'payment.discount_message';
  static const payment_promo_price = 'payment.promo_price';
  static const payment_lowest_price_ever = 'payment.lowest_price_ever';
  static const payment_claim_offer = 'payment.claim_offer';
  static const payment = 'payment';
  static const exercise_add_exercise = 'exercise.add_exercise';
  static const exercise_exercises = 'exercise.exercises';
  static const exercise_run_title = 'exercise.run.title';
  static const exercise_run_subtitle = 'exercise.run.subtitle';
  static const exercise_run = 'exercise.run';
  static const exercise_weight_lifting_title = 'exercise.weight_lifting.title';
  static const exercise_weight_lifting_subtitle = 'exercise.weight_lifting.subtitle';
  static const exercise_weight_lifting = 'exercise.weight_lifting';
  static const exercise_describe_exercise_title = 'exercise.describe_exercise.title';
  static const exercise_describe_exercise_subtitle = 'exercise.describe_exercise.subtitle';
  static const exercise_describe_exercise = 'exercise.describe_exercise';
  static const exercise_manual_entry_title = 'exercise.manual_entry.title';
  static const exercise_manual_entry_subtitle = 'exercise.manual_entry.subtitle';
  static const exercise_manual_entry = 'exercise.manual_entry';
  static const exercise = 'exercise';

}
