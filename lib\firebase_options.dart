// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
///
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDPDvN5egfZk-lrp6iDSVAEOV_C9fiihOE',
    appId: '1:996337025817:android:ae9023dee55d16e8a27b88',
    messagingSenderId: '996337025817',
    projectId: 'orange-ai-fe389',
    storageBucket: 'orange-ai-fe389.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyD6C3RTajq4_3mq9kvrLUsqhVT4NmG6lOo',
    appId: '1:996337025817:ios:0eeed98221aa4289a27b88',
    messagingSenderId: '996337025817',
    projectId: 'orange-ai-fe389',
    storageBucket: 'orange-ai-fe389.firebasestorage.app',
    androidClientId: '996337025817-301csbihrhllr1pkncnq2r6nhcjrni4a.apps.googleusercontent.com',
    iosClientId: '996337025817-mpjsgu8f9bcttuunk0o6gg9og6g6quul.apps.googleusercontent.com',
    iosBundleId: 'com.raizer.orangeai',
  );
}
