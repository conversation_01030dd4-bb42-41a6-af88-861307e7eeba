import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import '../../common/consts/typedef.dart';
import 'error_handler.dart';
import 'exceptions.dart';

mixin ApiHandler {
  bool _isSuccess(Response response) => [200, 201, 202].contains(response.statusCode);

  Future<Either<Failure, T>> handleApiCall<T>({required Future<Response> Function() apiCall, T Function(dynamic json)? fromJson}) async {
    try {
      final response = await apiCall();

      if (_isSuccess(response)) {
        if (fromJson != null) return Right(fromJson(response.data));
        if (T == Unit) return Right(unit as T);
        return Right(response.data as T);
      }

      return Left(ExceptionHandler.handleException(response));
    } catch (error) {
      return Left(ExceptionHandler.handleException(error));
    }
  }

  Future<Either<Failure, T>> getFile<T>({
    required Future<Response> Function() apiCall,
  }) async {
    try {
      final response = await apiCall();
      if (response.statusCode == 200) return Right(response.data as T);
      return Left(ExceptionHandler.handleException(response));
    } catch (error) {
      return Left(ExceptionHandler.handleException(error));
    }
  }

  Future<T> wrapHandlingApi<T>({required Future<Response> Function() tryCall, required FromJson<T> jsonConvert}) async {
    final response = await tryCall();
    if (response.statusCode == 200 || response.statusCode == 201 || response.statusCode == 202 || response.statusCode == 203 || response.statusCode == 204) {
      return jsonConvert(response.data);
    } else if (response.statusCode == 401) {
      throw UnauthenticatedFailure(
        message: response.data["message"].toString(),
      );
    } else if (response.statusCode == 403) {
      throw ForbiddenFailure(
        message: response.data["message"].toString(),
      );
    } else {
      throw ServerFailure(
        message: response.data["message"].toString(),
        statusCode: response.statusCode,
      );
    }
  }

}
