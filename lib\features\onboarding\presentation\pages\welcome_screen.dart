import 'package:cal/common/consts/app_keys.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/utils/shared_preferences_helper.dart';
import 'package:cal/common/widgets/large_button.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_wrapper.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:cal/common/widgets/app_image.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  late PageController _imageController;
  late PageController _textController;
  int _currentPage = 0;

  final List<OnboardingItem> _pages = [
    OnboardingItem(
      title: LocaleKeys.onboarding_one_photo.tr(),
      description: LocaleKeys.onboarding_one_photo_desc.tr(),
      imagePath: Assets.onboardingFirstWelcomeImage,
    ),
    OnboardingItem(
      title: LocaleKeys.onboarding_halal.tr(),
      description: LocaleKeys.onboarding_halal_desc.tr(),
      imagePath: Assets.onboardingSecWelcomeImage,
      showHalalLogo: true,
    ),
    OnboardingItem(
      title: LocaleKeys.onboarding_health.tr(),
      description: LocaleKeys.onboarding_health_desc.tr(),
      imagePath: Assets.onboardingThirdWelcomeImage,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _imageController = PageController();
    _textController = PageController();

    _textController.addListener(() {
      // Sync only from _textController to _imageController
      if (!_imageController.hasClients || _imageController.page == _textController.page) return;

      _imageController.animateTo(_textController.position.pixels, duration: const Duration(milliseconds: 100), curve: Curves.fastOutSlowIn);
    });
  }

  @override
  void dispose() {
    _imageController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Stack(
                children: [
                  PageView.builder(
                    controller: _imageController,
                    itemCount: _pages.length,
                    physics: const NeverScrollableScrollPhysics(),
                    onPageChanged: (index) => setState(() => _currentPage = index),
                    itemBuilder: (context, index) {
                      final item = _pages[index];
                      return Stack(
                        children: [
                          Column(
                            children: [
                              AppImage.asset(
                                item.imagePath,
                                fit: BoxFit.contain,
                              ),
                            ],
                          ),
                          if (item.showHalalLogo)
                            const Positioned(
                              top: 16,
                              left: 16,
                              child: AppImage.asset(Assets.onboardingHalal),
                            ),
                        ],
                      );
                    },
                  ),
                  Positioned.fill(
                    bottom: 0,
                    child: Align(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        height: context.screenHeight * .4,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 50),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: PageView.builder(
                                controller: _textController,
                                itemCount: _pages.length,
                                onPageChanged: (index) {
                                  setState(() => _currentPage = index);
                                },
                                itemBuilder: (context, index) {
                                  final item = _pages[index];
                                  return SingleChildScrollView(
                                    padding: const EdgeInsets.symmetric(horizontal: 8),
                                    child: Column(
                                      children: [
                                        Text(
                                          item.title,
                                          style: context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w700),
                                          textAlign: TextAlign.center,
                                        ),
                                        const SizedBox(height: 12),
                                        Text(
                                          item.description,
                                          style: context.textTheme.titleSmall!
                                              .copyWith(fontWeight: FontWeight.w100, color: context.onSecondary.withAlpha(178)),
                                          textAlign: TextAlign.center,
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            _buildPageIndicator(),
            _buildStartButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildPageIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          _pages.length,
          (index) => Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            height: 8,
            width: 8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: _currentPage == index ? context.colorScheme.primary : Colors.grey.shade500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStartButton() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (_currentPage > 0)
            Expanded(
              child: LargeButton(
                onPressed: () {
                  _imageController.animateToPage(_currentPage - 1,
                      duration: const Duration(milliseconds: 500), curve: Curves.fastOutSlowIn);
                  _textController.animateToPage(_currentPage - 1, duration: const Duration(milliseconds: 500), curve: Curves.fastOutSlowIn);
                  _currentPage--;
                  setState(() {});
                },
                text: LocaleKeys.common_back.tr(),
                circularRadius: 16,
                backgroundColor: Colors.grey.shade300,
                textStyle: context.textTheme.bodyMedium!.copyWith(color: Colors.black),
              ),
            ),
          if (_currentPage > 0) const SizedBox(width: 16),
          Expanded(
            child: LargeButton(
              onPressed: () {
                if (_currentPage == 2) {
                  context.pushAndRemoveUntil(const OnboardingWrapper());
                  ShPH.saveData(key: AppKeys.shouldShowWelcomeScreen, value: false);
                } else {
                  _imageController.animateToPage(
                    _currentPage + 1,
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.fastOutSlowIn,
                  );
                  _textController.animateToPage(
                    _currentPage + 1,
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.fastOutSlowIn,
                  );
                  _currentPage++;
                  setState(() {});
                }
              },
              text: _currentPage == 2 ? LocaleKeys.onboarding_start.tr() : LocaleKeys.onboarding_next.tr(),
              circularRadius: 16,
              backgroundColor: context.primaryColor,
              textStyle: context.textTheme.bodyMedium!.copyWith(color: context.onPrimaryColor),
            ),
          ),
        ],
      ),
    );
  }
}

class OnboardingItem {
  final String title;
  final String description;
  final String imagePath;
  final bool showHalalLogo;

  OnboardingItem({
    required this.title,
    required this.description,
    required this.imagePath,
    this.showHalalLogo = false,
  });
}
