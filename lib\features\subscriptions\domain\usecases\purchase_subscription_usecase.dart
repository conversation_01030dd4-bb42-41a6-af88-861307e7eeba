import 'package:cal/features/subscriptions/domain/entities/subscription_plan_entity.dart';
import 'package:cal/features/subscriptions/domain/repositories/subscription_repository.dart';
import 'package:dartz/dartz.dart';

class PurchaseSubscriptionUseCase {
  final SubscriptionRepository repository;

  PurchaseSubscriptionUseCase(this.repository);

  Future<Either<String, void>> call(SubscriptionPlanEntity plan) async {
    return await repository.purchaseSubscription(plan);
  }
}
