import 'package:flutter/material.dart';

import '../entities/user_entity.dart';
import '../entities/auth_token_entity.dart';

// Domain Repository Interface
abstract class AuthenticationRepository {
  Future<UserEntity> signInWithApple({required BuildContext context});
  Future<UserEntity> signInWithGoogle();
  Future<void> signOut();
  Future<UserEntity?> getCurrentUser();
  Future<AuthTokenEntity?> getCurrentToken();
  Future<bool> isAuthenticated();
}

