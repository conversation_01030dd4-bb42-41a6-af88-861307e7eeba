import 'package:flutter/material.dart';

import '../entities/user_entity.dart';
import '../repositories/authentication_repository.dart';

// Use Case for Sign In with Apple
class SignInWithAppleUseCase {
  final AuthenticationRepository repository;

  SignInWithAppleUseCase(this.repository);

  Future<UserEntity> call({required BuildContext context}) async {
    return await repository.signInWithApple(context: context);
  }
}
