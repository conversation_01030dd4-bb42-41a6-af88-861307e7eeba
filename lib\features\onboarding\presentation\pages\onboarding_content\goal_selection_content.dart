import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_option.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Goal Selection Screen
class GoalSelectionContent extends StatefulWidget {
  const GoalSelectionContent({super.key, required this.isFromSettings});

  final bool isFromSettings;

  @override
  State<GoalSelectionContent> createState() => _GoalSelectionContentState();
}

class _GoalSelectionContentState extends State<GoalSelectionContent> {
  @override
  void initState() {
    super.initState();
    context.read<OnboardingBloc>().add(RegisterScreenValidation((state) => state.goal != null));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_what_is_your_goal.tr(),
          description: LocaleKeys.onboarding_we_will_use_this_information_to_personalize_your_plan.tr(),
          contentWidgets: [
            const SizedBox(height: 60),
            ...Goal.values
                .map(
                  (goal) => OnboardingOption(
                    isSelected: state.goal == goal,
                    text: goal.localizedName,
                    onSelected: () => context.read<OnboardingBloc>().add(UpdateGoal(goal, widget.isFromSettings)),
                  ),
                )
                .toList(),
          ],
        );
      },
    );
  }
}
