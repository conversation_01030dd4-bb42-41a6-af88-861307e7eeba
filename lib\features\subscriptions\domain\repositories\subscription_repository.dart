import 'package:cal/features/subscriptions/domain/entities/subscription_plan_entity.dart';
import 'package:dartz/dartz.dart';
import 'package:in_app_purchase/in_app_purchase.dart' as iap;

abstract class SubscriptionRepository {
  /// Get available subscription plans
  Future<Either<String, List<SubscriptionPlanEntity>>> getSubscriptionPlans();
  
  /// Purchase a subscription
  Future<Either<String, void>> purchaseSubscription(SubscriptionPlanEntity plan);
  
  /// Restore previously purchased subscriptions
  Future<Either<String, void>> restorePurchases();
  
  /// Verify a purchase
  Future<Either<String, String>> verifyPurchase(iap.PurchaseDetails purchaseDetails);
  
  /// Get active subscription ID
  Future<Either<String, String?>> getActiveSubscriptionId();
  
  /// Listen to purchase updates
  Stream<iap.PurchaseDetails> get purchaseUpdates;
  
  /// Check if store is available
  Future<Either<String, bool>> isStoreAvailable();
}
