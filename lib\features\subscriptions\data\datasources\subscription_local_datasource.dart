import 'package:cal/common/consts/app_keys.dart';
import 'package:cal/common/utils/shared_preferences_helper.dart';
import 'package:cal/features/subscriptions/data/models/subscription_plan_model.dart';
import 'package:dartz/dartz.dart';

abstract class SubscriptionLocalDataSource {
  /// Get active subscription ID from local storage
  Future<Either<String, String?>> getActiveSubscriptionId();
  
  /// Save active subscription ID to local storage
  Future<Either<String, bool>> saveActiveSubscriptionId(String subscriptionId);
  
  /// Get mock subscription plans when store is not available
  Future<Either<String, List<SubscriptionPlanModel>>> getMockSubscriptionPlans();
}

class SubscriptionLocalDataSourceImpl implements SubscriptionLocalDataSource {
  @override
  Future<Either<String, String?>> getActiveSubscriptionId() async {
    try {
      final activeSubscriptionId = ShPH.getData(key: AppKeys.activeSubscriptionId);
      return Right(activeSubscriptionId);
    } catch (e) {
      return Left('Failed to get active subscription ID: $e');
    }
  }
  
  @override
  Future<Either<String, bool>> saveActiveSubscriptionId(String subscriptionId) async {
    try {
      await ShPH.saveData(key: AppKeys.activeSubscriptionId, value: subscriptionId);
      return const Right(true);
    } catch (e) {
      return Left('Failed to save active subscription ID: $e');
    }
  }
  
  @override
  Future<Either<String, List<SubscriptionPlanModel>>> getMockSubscriptionPlans() async {
    try {
      return Right(SubscriptionPlanModel.getMockPlans());
    } catch (e) {
      return Left('Failed to get mock subscription plans: $e');
    }
  }
}
