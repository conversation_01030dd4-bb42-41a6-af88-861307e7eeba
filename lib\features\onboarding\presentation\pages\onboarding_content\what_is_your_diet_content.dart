import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_option.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Goal Selection Screen
class WhatIsYourDietContent extends StatefulWidget {
  const WhatIsYourDietContent({super.key});

  @override
  State<WhatIsYourDietContent> createState() => _GoalSelectionContentState();
}

class _GoalSelectionContentState extends State<WhatIsYourDietContent> {
  @override
  void initState() {
    super.initState();
    context.read<OnboardingBloc>().add(RegisterScreenValidation((state) => state.diet != null));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_what_is_your_diet.tr(),
          centerContent: true,
          contentWidgets: [
            ...Diet.values
                .map(
                  (diet) => OnboardingOption(
                    isSelected: state.diet == diet,
                    imagePath: diet.icon,
                    text: diet.localizedName,
                    onSelected: () => context.read<OnboardingBloc>().add(UpdateWhatIsYourDiet(diet)),
                  ),
                )
                .toList(),
          ],
        );
      },
    );
  }
}
