import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wheel_slider/wheel_slider.dart';
import 'package:throttling/throttling.dart';

class TargetWeightSelector extends StatefulWidget {
  final double initialWeight;
  final int currentWeight;
  final bool isWeightLoss;
  final ValueChanged<double> onWeightChanged;

  const TargetWeightSelector({
    super.key,
    required this.initialWeight,
    required this.currentWeight,
    required this.isWeightLoss,
    required this.onWeightChanged,
  });

  @override
  State<TargetWeightSelector> createState() => _TargetWeightSelectorState();
}

class _TargetWeightSelectorState extends State<TargetWeightSelector> {
  late double selectedWeight;
  late double minWeight;
  late double maxWeight;
  late Throttling throttler;

  @override
  void initState() {
    super.initState();

    // Initialize throttler
    throttler = Throttling(duration: const Duration(milliseconds: 300));

    final int weight = widget.currentWeight;

    if (widget.isWeightLoss) {
      // Allow weight loss down to a fraction of the current weight, but not less than 25
      minWeight = (weight * 0.5).clamp(25.0, weight - 0.1);
      maxWeight = weight - 0.1; // Set maxWeight for weight loss

      // Use the initialWeight from the widget, which comes from the state
      selectedWeight = widget.initialWeight;

      // Make sure it's within valid range
      if (selectedWeight > maxWeight || selectedWeight < minWeight) {
        selectedWeight = maxWeight;
      }
    } else {
      // Allow weight gain up to a multiple of current weight, no upper bound
      double multiplier;

      if (weight < 40) {
        multiplier = 3.0; // skinny users can gain a lot
      } else if (weight < 60) {
        multiplier = 2.0;
      } else if (weight < 80) {
        multiplier = 1.6;
      } else {
        multiplier = 1.3;
      }

      minWeight = (weight + 0.1).clamp(25.0, double.infinity);
      maxWeight = weight * multiplier;

      // Use the initialWeight from the widget, which comes from the state
      selectedWeight = widget.initialWeight;

      // Make sure it's within valid range
      if (selectedWeight < minWeight || selectedWeight > maxWeight) {
        selectedWeight = minWeight;
      }
    }

    // Immediately update the bloc with the selected weight to ensure consistency
    widget.onWeightChanged(selectedWeight);
  }

  @override
  void didUpdateWidget(TargetWeightSelector oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the current weight or weight loss status changes, recalculate ranges
    if (oldWidget.currentWeight != widget.currentWeight || oldWidget.isWeightLoss != widget.isWeightLoss) {
      final int weight = widget.currentWeight;

      if (widget.isWeightLoss) {
        minWeight = (weight * 0.5).clamp(25.0, weight - 0.1);
        maxWeight = weight - 0.1;

        // Ensure selected weight is still in valid range
        if (selectedWeight > maxWeight || selectedWeight < minWeight) {
          selectedWeight = maxWeight;
          widget.onWeightChanged(selectedWeight);
        }
      } else {
        double multiplier;
        if (weight < 40) {
          multiplier = 3.0;
        } else if (weight < 60) {
          multiplier = 2.0;
        } else if (weight < 80) {
          multiplier = 1.6;
        } else {
          multiplier = 1.3;
        }

        minWeight = (weight + 0.1).clamp(25.0, double.infinity);
        maxWeight = weight * multiplier;

        // Ensure selected weight is still in valid range
        if (selectedWeight < minWeight || selectedWeight > maxWeight) {
          selectedWeight = minWeight;
          widget.onWeightChanged(selectedWeight);
        }
      }
    }

    // If the initial weight changes significantly, update the selected weight
    if ((oldWidget.initialWeight - widget.initialWeight).abs() > 1.0) {
      selectedWeight = widget.initialWeight;

      // Ensure it's within valid range
      if (widget.isWeightLoss) {
        if (selectedWeight > maxWeight || selectedWeight < minWeight) {
          selectedWeight = maxWeight;
        }
      } else {
        if (selectedWeight < minWeight || selectedWeight > maxWeight) {
          selectedWeight = minWeight;
        }
      }

      widget.onWeightChanged(selectedWeight);
    }
  }

  @override
  void dispose() {
    throttler.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Calculate total items with decimal precision (steps of 0.1)
    final totalItems = ((maxWeight - minWeight) * 10).round() + 1;

    // Calculate the correct initial index for the wheel slider
    // This needs to be precise to position the wheel correctly
    final initialIndex = ((selectedWeight - minWeight) * 10).round();

    // Ensure the index is within valid bounds
    final safeInitialIndex = initialIndex.clamp(0, totalItems - 1);

    return Column(
      children: [
        Text(
          '${selectedWeight.toStringAsFixed(1)}Kg',
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 80,
          child: WheelSlider(
            isInfinite: false,
            totalCount: totalItems,
            initValue: safeInitialIndex,
            isVibrate: false,
            onValueChanged: (index) {
              final newWeight = minWeight + (index / 10);

              // Update UI immediately for responsive feel
              setState(() => selectedWeight = newWeight);

              // Use throttling to debounce rapid changes
              throttler.throttle(() {
                HapticFeedback.selectionClick();
                widget.onWeightChanged(newWeight);
              });
            },
            itemSize: 20,
          ),
        ),
      ],
    );
  }
}
