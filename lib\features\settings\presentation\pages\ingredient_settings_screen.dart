import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/core/local_models/daily_data_model/daily_data_model.dart';
import 'package:cal/core/local_models/daily_data_model/daily_user_info_service.dart';
import 'package:cal/features/settings/presentation/widgets/personal_details_app_bar.dart';
import 'package:flutter/material.dart';

import '../../../../generated/assets.dart';
import '../../../onboarding/presentation/pages/onboarding_wrapper.dart';
import '../widgets/cals_metric_card.dart';
import '../widgets/edit_ingredient_card.dart';

class IngredientSettingsScreen extends StatefulWidget {
  const IngredientSettingsScreen({super.key});

  @override
  State<IngredientSettingsScreen> createState() => _IngredientSettingsScreenState();
}

class _IngredientSettingsScreenState extends State<IngredientSettingsScreen> {
  DailyUserDataModel? dailyData;

  List<String> images = [Assets.imagesCals, Assets.imagesProtien, Assets.imagesCarbs, Assets.imagesFats];

  List<Color> colors = [const Color(0xffFE761F), const Color(0xffE55B35), const Color(0xffFFA76E), const Color(0xff4277FF)];

  List<String> title = ['السعرات المستهدفة', 'البروتين المستهدف', 'الكاربوهيدرات المستهدفة', 'الدهون المستهدفة'];

  List<TextEditingController> controllers = [];

  List<FocusNode> nodes = List.generate(4, (i) => FocusNode());

  @override
  void initState() {
    super.initState();
    fetchData();
  }

  fetchData() async {
    dailyData = await DailyUserInfoService.getDailyBmi(startDate: DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day));
    if (dailyData != null) {
      controllers = [
        TextEditingController(text: dailyData?.targetCalories.toString()),
        TextEditingController(text: dailyData?.targetProtein.toString()),
        TextEditingController(text: dailyData?.targetCarbs.toString()),
        TextEditingController(text: dailyData?.targetFat.toString()),
      ];
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.background,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsetsDirectional.symmetric(horizontal: 20),
          child: Column(
            children: [
              const SizedBox(height: 40),
              const PersonalDetailsAppBar(title: 'ضبط العناصر الغذائية'),
              controllers.isNotEmpty
                  ? CalsMetricCard(
                      data: List.generate(
                          3,
                          (i) => MacroData(
                              title[i + 1], double.parse(controllers[i + 1].text == '' ? '0' : controllers[i + 1].text), colors[i + 1])),
                    )
                  : const SizedBox.shrink(),
              const SizedBox(height: 30),
              Padding(
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 10),
                child: Column(
                  spacing: 12,
                  children: List.generate(
                    4,
                    (index) => controllers.isNotEmpty
                        ? EditIngredientCard(
                            color: colors[index],
                            image: images[index],
                            title: title[index],
                            controller: controllers[index],
                            focusNode: nodes[index],
                            onChanged: (val) {
                              onMacroChanged(index, val);
                            },
                            value: 10.toString(),
                          )
                        : const SizedBox.shrink(),
                  ),
                ),
              ),
              const SizedBox(height: 32),
              Padding(
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 10),
                child: InkWell(
                  borderRadius: BorderRadius.circular(14),
                  onTap: () async {
                    await context.push(OnboardingWrapper(isFromSettingsToChangeTarget: true, model: dailyData)).then((val) {
                      fetchData();
                    });
                  },
                  child: Container(
                    height: 50,
                    width: context.screenWidth,
                    decoration: BoxDecoration(
                      color: context.onPrimaryColor,
                      border: Border.all(color: context.onSecondary, width: 1),
                      borderRadius: BorderRadius.circular(14),
                    ),
                    child: Center(
                      child: Text(
                        'توليد تلقائي للأهداف',
                        style: context.textTheme.bodyMedium!.copyWith(color: context.onSecondary, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  onMacroChanged(int index, String val) async {
    double value = double.tryParse(val) ?? 0.0;
    double calories;

    if (index == 0) {
      // Calories entered directly
      controllers[1].text = ((value * 0.20) / 4).toStringAsFixed(1); // Protein (g)
      controllers[2].text = ((value * 0.50) / 4).toStringAsFixed(1); // Carbs (g)
      controllers[3].text = ((value * 0.30) / 9).toStringAsFixed(1); // Fat (g)

      await DailyUserInfoService.saveDailyData(dailyData!.copyWith(
        targetCalories: value,
        targetProtein: (value * 0.20) / 4,
        targetCarbs: (value * 0.50) / 4,
        targetFat: (value * 0.30) / 9,
      ));
    }

    if (index == 1) {
      // Protein entered: only update Calories
      double proteinCals = value * 4;
      calories = proteinCals / 0.20;
      controllers[0].text = calories.toStringAsFixed(1); // Update total calories

      await DailyUserInfoService.saveDailyData(dailyData!.copyWith(
        targetCalories: calories,
        targetProtein: value,
      ));
    }

    if (index == 2) {
      // Carbs entered: only update Calories
      double carbCals = value * 4;
      calories = carbCals / 0.50;
      controllers[0].text = calories.toStringAsFixed(1);

      await DailyUserInfoService.saveDailyData(dailyData!.copyWith(
        targetCalories: calories,
        targetCarbs: value,
      ));
    }

    if (index == 3) {
      // Fat entered: only update Calories
      double fatCals = value * 9;
      calories = fatCals / 0.30;
      controllers[0].text = calories.toStringAsFixed(1);

      await DailyUserInfoService.saveDailyData(dailyData!.copyWith(
        targetCalories: calories,
        targetFat: value,
      ));
    }

    setState(() {});
  }
}
