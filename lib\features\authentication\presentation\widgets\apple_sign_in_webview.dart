import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cal/features/authentication/data/models/auth_token_model.dart';
import 'package:cal/core/mobile_id_helper.dart';
import 'package:cal/common/consts/app_keys.dart';
import 'package:cal/common/utils/shared_preferences_helper.dart';
import 'package:cal/core/network/http_client.dart';
import 'package:cal/core/network/api_handler.dart';

class AppleSignInWebView extends StatefulWidget {
  final Function(AuthTokenModel) onSuccess;
  final Function(String) onError;
  final HTTPClient httpClient;

  const AppleSignInWebView({
    Key? key,
    required this.onSuccess,
    required this.onError,
    required this.httpClient,
  }) : super(key: key);

  @override
  State<AppleSignInWebView> createState() => _AppleSignInWebViewState();
}

class _AppleSignInWebViewState extends State<AppleSignInWebView> with <PERSON><PERSON><PERSON><PERSON><PERSON> {
  late WebViewController _controller;

  @override
  void initState() {
    super.initState();
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar if needed
          },
          onPageStarted: (String url) {},
          onPageFinished: (String url) {},
          onWebResourceError: (WebResourceError error) {
            widget.onError('WebView error: ${error.description}');
          },
          onNavigationRequest: (NavigationRequest request) {
            // Check for redirect URL to capture authentication tokens
            if (request.url.contains('code=')) {
              _handleRedirectUrl(request.url);
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(
          'https://appleid.apple.com/auth/authorize?response_type=code%20id_token&response_mode=fragment&client_id=YOUR_CLIENT_ID&redirect_uri=YOUR_REDIRECT_URI&scope=name%20email'));
    // Replace YOUR_CLIENT_ID and YOUR_REDIRECT_URI with actual values from your Apple Developer account
  }

  void _handleRedirectUrl(String url) async {
    try {
      // Parse the URL to extract code and id_token
      final uri = Uri.parse(url);
      final fragment = uri.fragment;
      final fragmentParams = Uri.splitQueryString(fragment.replaceFirst('#', '?'));

      final code = fragmentParams['code'];
      final idToken = fragmentParams['id_token'];

      if (code == null || idToken == null) {
        widget.onError('Failed to extract authentication tokens from redirect URL');
        return;
      }

      // Use the tokens to authenticate with Firebase
      final oauthCredential = OAuthProvider('apple.com').credential(
        idToken: idToken,
        accessToken: code,
      );

      final userCredential = await FirebaseAuth.instance.signInWithCredential(oauthCredential);
      final user = userCredential.user;

      if (user == null) {
        widget.onError('User not found after Apple sign-in');
        return;
      }

      final idTokenResult = await user.getIdTokenResult();

      // Send data to backend API
      final String mobileId = await MobileIdHelper.getMobileId();
      final appleTokenData = {
        'authorization_code': code,
        'identity_token': idToken,
        'user_identifier': user.uid,
        'email': user.email,
        'given_name': user.displayName?.split(' ').first ?? '',
        'family_name': user.displayName?.split(' ').last ?? '',
        'mobile_id': mobileId,
      };

      final apiResult = await handleApiCall(
        apiCall: () => widget.httpClient.post(
          '/api/auth/apple',
          data: appleTokenData,
          headers: {'Content-Type': 'application/json'},
        ),
      );

      apiResult.fold(
        (failure) {
          widget.onError('API call failed during Apple Sign-In: $failure');
        },
        (response) {
          final token = response['token'];

          if (token != null && token is String) {
            ShPH.saveData(key: AppKeys.token, value: token);
            widget.onSuccess(AuthTokenModel(
              accessToken: idTokenResult.token!,
              expiresAt: idTokenResult.expirationTime!,
            ));
          } else {
            widget.onError('Token not found in response');
          }
        },
      );
    } catch (e) {
      widget.onError('Error handling redirect URL: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sign in with Apple'),
      ),
      body: WebViewWidget(controller: _controller),
    );
  }
}
