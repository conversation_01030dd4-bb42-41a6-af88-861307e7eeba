import 'package:cal/core/local_models/user_model/user_model.dart';
import 'package:cal/features/onboarding/domain/repositories/onboarding_repository.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class LocalSaveUpdateUserUseCase {

  final OnboardingRepository onboardingRepository;
  LocalSaveUpdateUserUseCase({required this.onboardingRepository});

  Future<void> saveUpdateUserData(UserModel user, bool isSave){
    return isSave ? onboardingRepository.saveUserData(user: user) : onboardingRepository.updateUserData(user);
  }

}