import 'package:cal/common/extentions/colors_extension.dart';
import 'package:flutter/material.dart';

import '../../../../common/widgets/app_image.dart';

class EditIngredientCard extends StatefulWidget {
  const EditIngredientCard({
    super.key,
    required this.image,
    required this.title,
    required this.value,
    required this.color,
    required this.controller,
    required this.focusNode,
    required this.onChanged,
  });

  final String image;
  final String title;
  final String value;
  final Color color;
  final TextEditingController controller;
  final FocusNode focusNode;
  final Function(String) onChanged;

  @override
  State<EditIngredientCard> createState() => _EditIngredientCardState();
}

class _EditIngredientCardState extends State<EditIngredientCard> {
  bool isEditing = false;

  @override
  void initState() {
    widget.focusNode.addListener(() {
      setState(() {
        isEditing = widget.focusNode.hasFocus;
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 800),
              curve: Curves.easeInOut,
              tween: Tween<double>(
                begin: 0,
                end: .5,
              ),
              builder: (context, value, _) {
                return SizedBox(
                  width: 60,
                  height: 60,
                  child: CircularProgressIndicator(
                    value: value,
                    strokeWidth: 4.5,
                    backgroundColor: context.onSecondary.withAlpha(12),
                    valueColor: AlwaysStoppedAnimation<Color>(widget.color),
                  ),
                );
              },
            ),
            CircleAvatar(
              radius: 15,
              backgroundColor: context.onSecondary.withAlpha(12),
              child: AppImage.asset(widget.image),
            ),
          ],
        ),
        const SizedBox(width: 15),
        Expanded(
          child: Container(
              height: 75,
              padding: const EdgeInsetsDirectional.only(start: 8, top: 6, bottom: 6, end: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: context.onPrimaryColor,
                border: Border.all(
                  color: isEditing ? context.primaryColor : context.onSecondary.withAlpha(100),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                    style: context.textTheme.bodySmall!.copyWith(
                      color: context.onSecondary.withAlpha(127),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(
                    height: 41,
                    child: TextFormField(
                      controller: widget.controller,
                      keyboardType: TextInputType.number,
                      textInputAction: TextInputAction.done,
                      cursorColor: context.primaryColor,
                      maxLines: 1,
                      focusNode: widget.focusNode,
                      onTap: () {
                        setState(() {
                          isEditing = true;
                        });
                      },
                      onEditingComplete: () => FocusScope.of(context).unfocus(),
                      textAlign: TextAlign.start,
                      onChanged: widget.onChanged,
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        errorBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                      ),
                      style: context.textTheme.bodySmall!.copyWith(
                        fontWeight: FontWeight.w900,
                        color: context.onSecondary,
                      ),
                    ),
                  ),
                ],
              )),
        ),
      ],
    );
  }
}
