import 'package:cal/features/subscriptions/domain/entities/subscription_plan_entity.dart';
import 'package:cal/features/subscriptions/domain/repositories/subscription_repository.dart';
import 'package:dartz/dartz.dart';

class GetSubscriptionPlansUseCase {
  final SubscriptionRepository repository;

  GetSubscriptionPlansUseCase(this.repository);

  Future<Either<String, List<SubscriptionPlanEntity>>> call() async {
    return await repository.getSubscriptionPlans();
  }
}
