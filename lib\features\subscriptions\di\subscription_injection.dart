import 'package:cal/features/subscriptions/data/datasources/subscription_local_datasource.dart';
import 'package:cal/features/subscriptions/data/datasources/subscription_remote_datasource.dart';
import 'package:cal/features/subscriptions/data/repositories/subscription_repository_impl.dart';
import 'package:cal/features/subscriptions/domain/repositories/subscription_repository.dart';
import 'package:cal/features/subscriptions/domain/usecases/get_active_subscription_id_usecase.dart';
import 'package:cal/features/subscriptions/domain/usecases/get_subscription_plans_usecase.dart';
import 'package:cal/features/subscriptions/domain/usecases/is_store_available_usecase.dart';
import 'package:cal/features/subscriptions/domain/usecases/purchase_subscription_usecase.dart';
import 'package:cal/features/subscriptions/domain/usecases/restore_purchases_usecase.dart';
import 'package:cal/features/subscriptions/domain/usecases/verify_purchase_usecase.dart';
import 'package:cal/features/subscriptions/presentation/bloc/subscription_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

final sl = GetIt.instance;

Future<void> initSubscriptionDependencies() async {
  // BLoC
  sl.registerFactory(
    () => SubscriptionBloc(
      getSubscriptionPlansUseCase: sl(),
      purchaseSubscriptionUseCase: sl(),
      restorePurchasesUseCase: sl(),
      verifyPurchaseUseCase: sl(),
      getActiveSubscriptionIdUseCase: sl(),
      isStoreAvailableUseCase: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetSubscriptionPlansUseCase(sl()));
  sl.registerLazySingleton(() => PurchaseSubscriptionUseCase(sl()));
  sl.registerLazySingleton(() => RestorePurchasesUseCase(sl()));
  sl.registerLazySingleton(() => VerifyPurchaseUseCase(sl()));
  sl.registerLazySingleton(() => GetActiveSubscriptionIdUseCase(sl()));
  sl.registerLazySingleton(() => IsStoreAvailableUseCase(sl()));

  // Repository
  sl.registerLazySingleton<SubscriptionRepository>(
    () => SubscriptionRepositoryImpl(
      localDataSource: sl(),
      remoteDataSource: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<SubscriptionLocalDataSource>(
    () => SubscriptionLocalDataSourceImpl(),
  );
  sl.registerLazySingleton<SubscriptionRemoteDataSource>(
    () => SubscriptionRemoteDataSourceImpl(),
  );

  // External
  sl.registerLazySingleton(() => InAppPurchase.instance);
}
