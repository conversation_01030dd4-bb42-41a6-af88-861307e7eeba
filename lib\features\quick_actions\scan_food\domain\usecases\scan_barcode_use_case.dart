import 'package:cal/common/consts/typedef.dart';
import 'package:cal/features/quick_actions/scan_food/domain/repositories/scan_food_repository.dart';
import 'package:injectable/injectable.dart';

import '../../../../../core/local_models/food_model/food_model.dart';

@lazySingleton
class ScanBarcodeUseCase {
  final ScanFoodRepository scanFoodRepository;

  ScanBarcodeUseCase({required this.scanFoodRepository});

  DataResponse<FoodModel> call(ScanBarcodeParams params) {
    return scanFoodRepository.scanBarcode(params);
  }
}

class ScanBarcodeParams with Params {
  final String barcode;

  ScanBarcodeParams({required this.barcode});

  @override
  QueryParams getParams() => {"code": barcode};
}
