part of 'nutrition_bloc.dart';

class NutritionEvent extends Equatable {
  const NutritionEvent();

  @override
  List<Object> get props => [];
}

class LoadDailyNutritionData extends NutritionEvent {
  final DateTime date;

  const LoadDailyNutritionData({required this.date});

  @override
  List<Object> get props => [date];
}

class LoadUserNutritionTargets extends NutritionEvent {}

class InitDailyUserData extends NutritionEvent {
  final DateTime date;

  const InitDailyUserData({required this.date});
}