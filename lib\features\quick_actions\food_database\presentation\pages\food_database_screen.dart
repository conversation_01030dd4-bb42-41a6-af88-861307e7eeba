import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/common/widgets/custom_text_field.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/search_meals_use_case.dart';
import 'package:cal/features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/filter_content/all_meals_content.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/filter_content/favorites_content.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/filter_content/my_meals_content.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:throttling/throttling.dart';

class FoodDatabaseScreen extends StatefulWidget {
  const FoodDatabaseScreen({super.key, this.tapIndex});

  final int? tapIndex;

  @override
  State<FoodDatabaseScreen> createState() => _FoodDatabaseScreenState();
}

class _FoodDatabaseScreenState extends State<FoodDatabaseScreen> {
  final TextEditingController mealController = TextEditingController();
  bool showDatabaseList = false;

  bool loadDatabase = true;

  final List<String> _filters = [
    LocaleKeys.food_database_all.tr(),
    LocaleKeys.food_database_my_meals.tr(),
    LocaleKeys.food_database_favorite_meals.tr(),
  ];
  int _selectedFilterIndex = 0;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    mealController.addListener(() {
      setState(() {
        _searchQuery = mealController.text.trim().toLowerCase();
        showDatabaseList = _searchQuery.isNotEmpty;
        if (loadDatabase == true) {
          context.read<FoodDatabaseBloc>().add(const LoadDatabaseFoodEvent());
          loadDatabase = false;
        }
      });
    });
    if (widget.tapIndex != null) {
      _selectedFilterIndex = widget.tapIndex!;
    }
  }

  final deb = Debouncing(duration: const Duration(milliseconds: 500));

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Scaffold(
          resizeToAvoidBottomInset: true,
          backgroundColor: context.background,
          appBar: AppBar(
            forceMaterialTransparency: true,
            elevation: 0,
            centerTitle: true,
            leading: Padding(
              padding: const EdgeInsets.only(right: 15.0),
              child: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  context.pop();
                },
              ),
            ),
            automaticallyImplyLeading: false,
            titleSpacing: 0,
            title: Text(
              LocaleKeys.food_database_food_database.tr(),
              style: context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w300, color: context.primaryColor),
              textAlign: TextAlign.start,
            ),
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsetsDirectional.only(end: 26, start: 26, top: 2, bottom: 50),
              child: Column(
                children: [
                  CustomTextField(
                    controller: mealController,
                    hint: LocaleKeys.food_database_describe_what_you_ate.tr(),
                    onChanged: (value) {
                      if (_selectedFilterIndex == 0) {
                        deb.debounce(() {
                          context.read<FoodDatabaseBloc>().add(SearchFoodEvent(params: SearchMealsParams(query: value)));
                        });
                      } else {
                        _searchQuery = value.toLowerCase();
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(children: [_buildFilterRow(widget.tapIndex)]),
                  const SizedBox(height: 16),
                  _buildFilterContent(),
                ],
              ),
            ),
          ),
        ));
  }

  Widget _buildFilterRow(int? tapIndex) {
    return Row(
      spacing: 24,
      children: List.generate(_filters.length, (index) {
        final isSelected = _selectedFilterIndex == index;
        return AppGestureDetector(
          onTap: () {
            setState(() {
              mealController.text = '';
              _selectedFilterIndex = index;
            });
          },
          child: Text(
            _filters[index],
            style: context.textTheme.bodyMedium!.copyWith(
              fontWeight: FontWeight.w400,
              color: isSelected ? context.primaryColor : context.onSecondary.withAlpha(178),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildFilterContent() {
    switch (_selectedFilterIndex) {
      case 0: // All
        return AllMealsContent(
          showDatabaseList: showDatabaseList,
          searchQuery: _searchQuery,
        );
      case 1: // My Meals
        return MyMealsContent(searchQuery: _searchQuery);

      case 2: // Favorite Meals
        return FavoritesContent(searchQuery: _searchQuery);

      default:
        return const SizedBox.shrink();
    }
  }
}
