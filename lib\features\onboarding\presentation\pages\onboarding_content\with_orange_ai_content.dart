import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class WithOrangeAiContent extends StatefulWidget {
  const WithOrangeAiContent({super.key});

  @override
  State<WithOrangeAiContent> createState() => _WhereDidHearOfUsState();
}

class _WhereDidHearOfUsState extends State<WithOrangeAiContent> {
  double withoutAIHeight = 0.0;
  double withAIHeight = 0.0;

  @override
  void initState() {
    super.initState();
    // Trigger animation
    Future.delayed(const Duration(milliseconds: 300), () {
      setState(() {
        withoutAIHeight = 70; // 20% of container
        withAIHeight = 110; // Double height (40% or 2x visual)
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_with_orange_ai_youll_lose_more_weight_than_with_regular_methods.tr(),
          centerContent: true,
          contentWidgets: [
            SizedBox(height: context.screenHeight * 0.08),
            Container(
              width: 400,
              height: 275,
              padding: const EdgeInsets.symmetric(vertical: 20),
              decoration: BoxDecoration(
                color: const Color(0xFFEFEFEF),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      // crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        _buildBar(
                          labelTop: LocaleKeys.onboarding_with.tr(),
                          fillHeight: withAIHeight,
                          fillColor: Colors.black,
                          labelInside: 'X2',
                        ),
                        const SizedBox(width: 22),
                        _buildBar(
                          labelTop: LocaleKeys.onboarding_without.tr(),
                          fillHeight: withoutAIHeight,
                          fillColor: Colors.grey,
                          labelInside: '20%',
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    LocaleKeys.onboarding_orange_make_it_easy.tr(),
                    style: const TextStyle(fontSize: 14, color: Colors.black87),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildBar({
    required String labelTop,
    required double fillHeight,
    required Color fillColor,
    required String labelInside,
  }) {
    const double barWidth = 90;
    const double barHeight = 160;

    return Column(
      // mainAxisAlignment: MainAxisAlignment.end,
      children: [
        const SizedBox(height: 8),
        Container(
          width: barWidth,
          height: barHeight,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 1000),
                curve: Curves.easeOut,
                width: barWidth,
                height: fillHeight,
                decoration: BoxDecoration(
                  color: fillColor,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(12),
                    bottom: Radius.circular(12),
                  ),
                ),
                alignment: Alignment.center,
                child: Text(
                  labelInside,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Positioned(
                top: 0,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    const SizedBox(height: 4),
                    Text(
                      labelTop,
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 14),
                    ),
                    Align(
                      alignment: Alignment.topCenter,
                      child: Text(
                        LocaleKeys.onboarding_orange.tr(),
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
