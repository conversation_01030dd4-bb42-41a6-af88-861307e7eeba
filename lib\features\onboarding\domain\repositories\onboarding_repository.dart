import 'package:cal/core/network/exceptions.dart';
import 'package:cal/features/onboarding/domain/usecases/submit_onboarding_usecase.dart';
import 'package:dartz/dartz.dart';

import '../../../../core/local_models/user_model/user_model.dart';

abstract class OnboardingRepository {
  Future<Either<Failure, void>> submitOnboarding({required SubmitOnboardingParams onboardingParams});

  Future<void> saveUserData({required UserModel user});

  Future<UserModel?> getUserData(UserModel user);

  Future<void> updateUserData(UserModel user);

  Future<void> clearUserData();

  Future<void> deleteUserData(UserModel user);

}
