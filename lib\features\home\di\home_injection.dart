import 'package:cal/features/home/<USER>/usecases/get_daily_user_data_usecase.dart';
import 'package:cal/features/home/<USER>/usecases/update_daily_user_data_usecase.dart';
import 'package:cal/features/home/<USER>/usecases/get_daily_exercises_usecase.dart';
import 'package:cal/features/home/<USER>/usecases/delete_exercise_usecase.dart';
import 'package:cal/features/home/<USER>/repositories/exercise_repository.dart';
import 'package:cal/features/home/<USER>/repositories/exercise_repository_impl.dart';
import 'package:get_it/get_it.dart';

void homeInjection(GetIt sl) {
  // Exercise Repository
  sl.registerLazySingleton<HomeExerciseRepository>(
    () => HomeExerciseRepositoryImpl(exerciseLocalDataSource: sl()),
  );

  // UseCases
  sl.registerLazySingleton<GetDailyUserDataUseCase>(
    () => GetDailyUserDataUseCase(repository: sl()),
  );
  sl.registerLazySingleton<UpdateDailyUserDataUseCase>(
    () => UpdateDailyUserDataUseCase(repository: sl()),
  );
  sl.registerLazySingleton<GetDailyExercisesUseCase>(
    () => GetDailyExercisesUseCase(sl()),
  );
  sl.registerLazySingleton<DeleteExerciseUseCase>(
    () => DeleteExerciseUseCase(sl()),
  );
}
