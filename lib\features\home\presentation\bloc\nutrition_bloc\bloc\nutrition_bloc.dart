import 'dart:async';
import 'dart:developer';

import 'package:bloc/bloc.dart';

import 'package:cal/features/home/<USER>/usecases/get_daily_user_data_usecase.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';

import '../../../../../../core/local_models/daily_data_model/daily_user_info_service.dart';

part 'nutrition_event.dart';
part 'nutrition_state.dart';

@injectable
class NutritionBloc extends Bloc<NutritionEvent, NutritionState> {
  final GetDailyUserDataUseCase getDailyUserDataUseCase;

  NutritionBloc({required this.getDailyUserDataUseCase}) : super(const NutritionState()) {
    on<LoadDailyNutritionData>(_onLoadNutritionDataForDate);
    on<InitDailyUserData>(_initDailyUserData);
  }

  FutureOr<void> _initDailyUserData(InitDailyUserData event, Emitter<NutritionState> emit) async {
    await DailyUserInfoService.initDailyData(DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day)).then((val) {
      add(LoadDailyNutritionData(date: event.date));
    });
  }

  void _onLoadNutritionDataForDate(LoadDailyNutritionData event, Emitter<NutritionState> emit) async {
    try {
      emit(state.copyWith(error: null));

      final data = await getDailyUserDataUseCase(event.date);

      emit(state.copyWith(
        targetCalories: data.targetCalories,
        targetCarbs: data.targetCarbs,
        targetProtein: data.targetProtein,
        targetFat: data.targetFat,
        consumedCalories: data.consumedCalories,
        consumedCarbs: data.consumedCarbs,
        consumedFat: data.consumedFat,
        consumedProtein: data.consumedProtein,
        burnedCalories: data.burnedCalories,
      ));

      log('Nutrition data loaded for: ${event.date}');
    } catch (e) {
      debugPrint("Error loading nutrition data: $e");
      emit(state.copyWith(error: e.toString()));
    }
  }
}
