import 'package:cal/core/config/endpoints.dart';
import 'package:cal/features/quick_actions/food_database/data/models/remote_food_database_model.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/create_meal_use_case.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/post_meal_to_log.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/search_meals_use_case.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../../core/network/api_handler.dart';
import '../../../../../core/network/exceptions.dart';
import '../../../../../core/network/http_client.dart';
import '../models/common_remote_food_model.dart';

@lazySingleton
class FoodRemoteDataSource with ApiHandler {
  final HTTPClient dioClient;

  FoodRemoteDataSource({required this.dioClient});

  Future<Either<Failure ,List<RemoteFoodDatabaseModel>>> searchMeals(SearchMealsParams params) async {
    return handleApiCall(
      apiCall: () => dioClient.get(FoodDatabaseEndPoint.search, queryParameters: params.getParams()),
      fromJson: (json) => remoteFoodDatabaseModelFromJson(json),
    );
  }

  Future<Either<Failure ,CommonRemoteFoodModel>> postMealToLog(PostMealToLogParams params) async {
    return handleApiCall(
      apiCall: () => dioClient.post('${params.isMeal ? FoodDatabaseEndPoint.logMeal : FoodDatabaseEndPoint.logFood}${params.foodId}'),
      fromJson: (json) => commonRemoteFoodModelFromJson(json),
    );
  }

  Future<Either<Failure ,CommonRemoteFoodModel>> createMeal(CreateMealParams params) async {
    return handleApiCall(
        apiCall: () => dioClient.post(FoodDatabaseEndPoint.createMeal, data: params.getBody()),
        fromJson: (json) => commonRemoteFoodModelFromJson(json),
    );
  }
}
