// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';

class ThemesColorScheme {
  static const _mainOrange = Color(0xffFE761F);

  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: _mainOrange,
    onPrimary: Colors.white,
    secondary: Color(0xffFFA366),
    onSecondary: Colors.black,
    tertiary: Color(0xFFB0BEC5), // added grey tertiary color
    onTertiary: Color(0xffdfdfdf),
    error: Color(0xffB00020),
    onError: Colors.white,

    // app background as light grey
    background: Color(0xFFF0F0F0),
    onBackground: Color(0xff1C1C1E),

    // surface elements white
    surface: Colors.white,
    onSurface: Color(0xff1C1C1E),

    primaryContainer: Color(0xff27AE60),
    onPrimaryContainer: Color(0xffEDEDED),
    secondaryContainer: Color(0xffFFF0E6),
    onSecondaryContainer: Color(0xff2C2C2E),
    tertiaryContainer: Color(0xFFECEFF1), // subtle grey container
    onTertiaryContainer: Color(0xff1C1C1E),
  );
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xffFF6400), // Bright orange for primary actions
    onPrimary: Colors.black, // Black text/icons on primary color
    secondary: Color(0xffFE8E22), // Slightly lighter orange for secondary actions
    onSecondary: Colors.white, // White text/icons on secondary color
    error: Color(0xffFF575A), // Red for errors
    onError: Colors.white, // White text/icons on error
    surface: Color(0xff202023), // Dark gray for surfaces
    onSurface: Color(0xffE0E0E0), // Light gray for text/icons on surfaces
    background: Color(0xff151515), // Deep black for background
    onBackground: Color(0xffB0B0B0), // Light gray for text/icons on background
    primaryContainer: Color(0xff333333), // Dark gray container for primary
    onPrimaryContainer: Color(0xffE0E0E0), // Light gray text on primary container
    secondaryContainer: Color(0xff444444), // Darker secondary container
    onSecondaryContainer: Color(0xffF0F0F0), // Light text on secondary container
    tertiary: Color(0xff2C2C2F), // Neutral tertiary for special elements
    onTertiary: Color(0xffE0E0E0), // Light gray on tertiary
    outline: Color(0xff5A5A5A), // Outline color for borders
    shadow: Colors.black, // Shadow color
    surfaceVariant: Color(0xff2A2A2D), // Slightly varied surface color
    onSurfaceVariant: Color(0xffB0B0B0), // Text on surface variant
  );
}
