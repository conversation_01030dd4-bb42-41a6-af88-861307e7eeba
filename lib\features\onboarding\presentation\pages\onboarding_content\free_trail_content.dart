import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:timelines_plus/timelines_plus.dart';

// Birth Date Screen
class FreeTrailContent extends StatelessWidget {
  const FreeTrailContent({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          contentWidgets: [
            const SizedBox(height: 40),

            // Main title
            Text(
              LocaleKeys.onboarding_Start_your_3day_free_trial.tr(),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 49),

            // Timeline
            Column(
              children: [
                // First timeline item
                TimelineItem(
                  icon: const Padding(
                    padding: EdgeInsets.all(7.0),
                    child: AppImage.asset(Assets.onboardingUnlock),
                  ),
                  title: LocaleKeys.onboarding_today.tr(),
                  description: LocaleKeys.onboarding_Unlock_all_the_apps_features.tr(),
                  isLast: false,
                  isfirst: true,
                  isCompleted: true,
                ),

                // Second timeline item
                TimelineItem(
                  icon: const Padding(
                    padding: EdgeInsets.all(7.0),
                    child: AppImage.asset(Assets.onboardingBell, size: 20),
                  ),
                  title: LocaleKeys.onboarding_after_two_days_reminder.tr(),
                  description: LocaleKeys.onboarding_Well_send_you_a_reminder.tr(),
                  isLast: false,
                  isCompleted: true,
                ),

                // Third timeline item
                TimelineItem(
                  icon: const Padding(
                    padding: EdgeInsets.all(7.0),
                    child: AppImage.asset(Assets.onboardingCrown, size: 20),
                  ),
                  title: LocaleKeys.onboarding_after_two_days_reminder.tr(),
                  description: 'سيتم خصم الرسوم من 28 مايو 2025 ما لم\nتقم بإلغاء الاشتراك قبل ذلك.',
                  isLast: true,
                  isCompleted: true,
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}

class TimelineItem extends StatelessWidget {
  final Widget icon;
  final String title;
  final String description;
  final bool isCompleted;
  final bool isLast;
  final bool isfirst;

  const TimelineItem({
    Key? key,
    required this.icon,
    required this.title,
    required this.description,
    this.isCompleted = false,
    this.isLast = false,
    this.isfirst = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: TimelineTile(
        nodeAlign: TimelineNodeAlign.start,

        // isLast: isLast,
        node: TimelineNode(
          indicatorPosition: 0.1,
          overlap: true,
          indicator: DotIndicator(
            size: 45,
            color: isLast ? Colors.black : context.primaryColor,
            child: icon,
          ),
          startConnector: isfirst == false
              ? SolidLineConnector(
                  color: isCompleted ? context.primaryColor.withAlpha(140) : Colors.grey.shade400,
                  thickness: 15,
                )
              : null,
          endConnector: ClipRRect(
            borderRadius:
                isLast ? const BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)) : BorderRadius.zero,
            child: SolidLineConnector(
              color: isLast
                  ? Colors.grey.shade400
                  : isCompleted
                      ? context.primaryColor.withAlpha(140)
                      : Colors.grey.shade400,
              thickness: 15,
            ),
          ),
        ),

        contents: Padding(
          padding: const EdgeInsets.only(left: 10, right: 10.0, bottom: 32, top: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade700,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
