part of 'recent_activity_bloc.dart';

sealed class RecentActivityEvent extends Equatable {
  const RecentActivityEvent();

  @override
  List<Object?> get props => [];
}

class LoadActivity extends RecentActivityEvent {
  final DateTime date;

  const LoadActivity({required this.date});
}

class AddFood extends RecentActivityEvent {
  final FoodModel meal;
  final bool isFromSearch;

  const AddFood({required this.meal, this.isFromSearch = false});

  @override
  List<Object?> get props => [meal];
}

class UpdateFood extends RecentActivityEvent {
  final FoodModel meal;

  const UpdateFood(this.meal);

  @override
  List<Object?> get props => [meal];
}

class UpdateFoodWithDifferences extends RecentActivityEvent {
  final FoodModel meal;
  final double caloriesDiff;
  final double carbsDiff;
  final double proteinDiff;
  final double fatDiff;

  const UpdateFoodWithDifferences({
    required this.meal,
    required this.caloriesDiff,
    required this.carbsDiff,
    required this.proteinDiff,
    required this.fatDiff,
  });

  @override
  List<Object?> get props => [meal, caloriesDiff, carbsDiff, proteinDiff, fatDiff];
}

class ClearFood extends RecentActivityEvent {}

class DeleteFood extends RecentActivityEvent {
  final FoodModel meal;

  const DeleteFood(this.meal);

  @override
  List<Object?> get props => [meal];
}

class AddExercise extends RecentActivityEvent {
  final ExerciseModel exercise;

  const AddExercise({required this.exercise});

  @override
  List<Object?> get props => [exercise];
}

class UpdateExercise extends RecentActivityEvent {
  final ExerciseModel exercise;

  const UpdateExercise(this.exercise);

  @override
  List<Object?> get props => [exercise];
}

class DeleteExercise extends RecentActivityEvent {
  final ExerciseModel exercise;

  const DeleteExercise(this.exercise);

  @override
  List<Object?> get props => [exercise];
}
