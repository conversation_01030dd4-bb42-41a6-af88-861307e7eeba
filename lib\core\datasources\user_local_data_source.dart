import 'package:injectable/injectable.dart';
import 'package:isar/isar.dart';

import '../local_models/user_model/user_model.dart';

@injectable
class UserLocalDataSource {
  final Isar _isar;

  UserLocalDataSource(this._isar);

  Future<void> saveUserData(UserModel user) async {
    await _isar.writeTxn(() async {
      await _isar.userModels.put(user);
    });
  }

  Future<UserModel?> getUserData() async {
    return await _isar.userModels.where().findAll().then((value) => value.lastOrNull);
  }

  Future<void> updateUser(UserModel updatedUser) async {
    await _isar.writeTxn(() async {
      await _isar.userModels.put(updatedUser);
    });
  }

  Future<void> deleteUser(UserModel user) async {
    await _isar.writeTxn(() async {
      await _isar.userModels.delete(user.id);
    });
  }

  Future<void> clearUser() async {
    await _isar.writeTxn(
      () async {
        await _isar.userModels.clear();
      },
    );
  }
}
