import 'package:flutter/material.dart';

class CustomSwitch extends StatelessWidget {
  final Function(bool)? onChanged;
  final bool value;
  const CustomSwitch({super.key, this.onChanged, required this.value});

  @override
  Widget build(BuildContext context) {
    return Transform.scale(
      scale: 0.8,
      child: Switch(
        value: value,
        onChanged: onChanged,
      ),
    );
  }
}
