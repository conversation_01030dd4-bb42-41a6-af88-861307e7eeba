import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:flutter/material.dart';
import 'package:cal/features/subscriptions/domain/entities/subscription_plan_entity.dart';
import 'package:cal/features/subscriptions/enums.dart';

class PaymentPlanCard extends StatelessWidget {
  final SubscriptionPlanEntity plan;
  final bool isSelected;
  final VoidCallback onTap;
  final String? badge;

  const PaymentPlanCard({
    Key? key,
    required this.plan,
    required this.isSelected,
    required this.onTap,
    this.badge,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppGestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? context.primaryColor : context.tertiary,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? context.primaryColor : Colors.grey,
                      width: 2,
                    ),
                    color: isSelected ? context.primaryColor : Colors.transparent,
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          size: 12,
                          color: Colors.white,
                        )
                      : null,
                ),
                if (badge != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: context.primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      badge!,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _getPlanTitle(),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _getPriceText(),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getPlanTitle() {
    switch (plan.type) {
      case SubscriptionPlanType.monthly:
        return 'شهري';
      case SubscriptionPlanType.yearly:
        return 'سنوي';
      case SubscriptionPlanType.quarterly:
        return 'ربع سنوي';
    }
  }

  String _getPriceText() {
    switch (plan.type) {
      case SubscriptionPlanType.monthly:
        return '${plan.price.toInt()} ل.س / بالشهر';
      case SubscriptionPlanType.yearly:
        final monthlyPrice = (plan.price / 12).toInt();
        return '$monthlyPrice ل.س / بالشهر';
      case SubscriptionPlanType.quarterly:
        final monthlyPrice = (plan.price / 3).toInt();
        return '$monthlyPrice ل.س / بالشهر';
    }
  }
}
