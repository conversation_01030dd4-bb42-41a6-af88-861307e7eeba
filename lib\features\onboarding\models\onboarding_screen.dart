import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';

/// Defines a screen in the onboarding flow
class OnboardingScreen {
  /// Unique identifier for the screen
  final String id;
  
  /// Function that determines if this screen should be shown based on current state
  /// If null, the screen is always shown
  final bool Function(OnboardingState)? shouldShow;
  
  const OnboardingScreen({
    required this.id,
    this.shouldShow,
  });
}
