import 'package:cal/common/extentions/colors_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

class OnboardingScreenTemplate extends StatelessWidget {
  final String? title;
  final String? description;

  final List<Widget> contentWidgets;
  final bool centerContent;

  const OnboardingScreenTemplate({
    this.title,
    this.description,
    this.centerContent = false,
    required this.contentWidgets,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsetsDirectional.symmetric(horizontal: 12, vertical: 5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 10),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (title != null)
                  Text(
                    title!,
                    style: context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w900),
                  ),
                if (description != null) ...[
                  const SizedBox(height: 15),
                  Text(
                    description!,
                    style: context.textTheme.headlineSmall!.copyWith(
                      fontWeight: FontWeight.w600,
                      color: context.onSecondary.withAlpha(178),
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 30),
            Column(
              mainAxisAlignment: centerContent ? MainAxisAlignment.center : MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: AnimationConfiguration.toStaggeredList(
                duration: const Duration(milliseconds: 500),
                childAnimationBuilder: (widget) => SlideAnimation(
                  verticalOffset: 50.0,
                  child: FadeInAnimation(child: widget),
                ),
                children: contentWidgets,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
/*CustomScrollView(
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
            sliver: SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (title != null)
                    Text(
                      title!,
                      style: context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w900),
                    ),
                  if (description != null) ...[
                    const SizedBox(height: 15),
                    Text(
                      description!,
                      style: context.textTheme.headlineSmall!.copyWith(
                        fontWeight: FontWeight.w600,
                        color: context.onSecondary.withAlpha(178),
                      ),
                    ),
                  ],
                  // const SizedBox(height: 35),
                ],
              ),
            ),
          ),
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
            sliver: SliverFillRemaining(
              hasScrollBody: true,
              child: Column(
                mainAxisAlignment: centerContent ? MainAxisAlignment.center : MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: AnimationConfiguration.toStaggeredList(
                  duration: const Duration(milliseconds: 500),
                  childAnimationBuilder: (widget) => SlideAnimation(
                    verticalOffset: 50.0,
                    child: FadeInAnimation(child: widget),
                  ),
                  children: contentWidgets,
                ),
              ),
            ),
          ),
        ],
      )*/
