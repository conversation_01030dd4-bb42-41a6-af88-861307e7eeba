import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/food_database_card.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FavoritesContent extends StatefulWidget {
  final String searchQuery;

  const FavoritesContent({super.key, required this.searchQuery});

  @override
  State<FavoritesContent> createState() => _FavoritesContentState();
}

class _FavoritesContentState extends State<FavoritesContent> {
  @override
  void initState() {
    context.read<FoodDatabaseBloc>().add(const LoadFavoriteFoodEvent());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
      builder: (context, state) {
        final filteredFavorites = state.myFavoriteList
            .where((meal) => meal.dish?.toLowerCase().contains(widget.searchQuery.toLowerCase()) ?? false)
            .toList();

        return Column(
          children: [
            const SizedBox(height: 10),
            filteredFavorites.isNotEmpty
                ? ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) => FoodDatabaseCard(
                      title: filteredFavorites[index].dish ?? "unknown",
                      cals: filteredFavorites[index].calories.toString(),
                    ),
                    separatorBuilder: (context, index) => const SizedBox(height: 16),
                    itemCount: filteredFavorites.length,
                  )
                : Align(
                    alignment: Alignment.center,
                    child: AppText.bodyMedium(
                      LocaleKeys.food_database_no_food.tr(),
                      color: context.onSecondary,
                      fontWeight: FontWeight.w300,
                    ),
                  ),
          ],
        );
      },
    );
  }
}
