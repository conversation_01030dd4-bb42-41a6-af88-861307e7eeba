import 'package:cal/common/extentions/colors_extension.dart';
import 'package:flutter/material.dart';
import 'package:timelines_plus/timelines_plus.dart';

class TimelineWidget extends StatefulWidget {
  const TimelineWidget({super.key});

  @override
  State<TimelineWidget> createState() => _TimelineWidgetState();
}

class _TimelineWidgetState extends State<TimelineWidget> with TickerProviderStateMixin {
  late final AnimationController _animationController;
  final int _itemCount = 3;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color _getIndicatorColor(int index, BuildContext context) {
    return index == 0 ? context.primaryColor : context.onSecondary;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 0),
      child: Timeline.tileBuilder(
        shrinkWrap: true,
        theme: TimelineThemeData(
          nodePosition: 0,
          color: context.primaryColor.withAlpha(75),
        ),
        builder: TimelineTileBuilder.connected(
          connectionDirection: ConnectionDirection.after,
          itemCount: _itemCount,
          contentsBuilder: (context, index) => _buildAnimatedContent(index, context),
          indicatorBuilder: (context, index) => _buildAnimatedIndicator(index, context),
          connectorBuilder: (context, index, type) => _buildAnimatedConnector(index, context),
        ),
      ),
    );
  }

  Widget _buildAnimatedContent(int index, BuildContext context) {
    final data = _getTimelineData(index);
    final intervalStart = index * (1.0 / _itemCount);
    final intervalEnd = intervalStart + (1.0 / _itemCount);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final animation = CurvedAnimation(
          parent: _animationController,
          curve: Interval(
            intervalStart,
            intervalEnd,
            curve: Curves.easeOut,
          ),
        );

        return Transform.translate(
          offset: Offset(0, 30 * (1 - animation.value)),
          child: Opacity(
            opacity: animation.value,
            child: Padding(
              padding: const EdgeInsets.only(right: 18, bottom: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    data['title'],
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: index == 0 ? context.primaryColor : context.onSecondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    data['description'],
                    style: TextStyle(
                      fontSize: 14,
                      color: context.onSecondary.withAlpha(144),
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedIndicator(int index, BuildContext context) {
    final data = _getTimelineData(index);
    final intervalStart = index * (1.0 / _itemCount);
    final intervalEnd = intervalStart + (1.0 / _itemCount);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final animation = CurvedAnimation(
          parent: _animationController,
          curve: Interval(
            intervalStart,
            intervalEnd,
            curve: Curves.elasticOut,
          ),
        );

        return Transform.translate(
          offset: Offset(0, 30 * (1 - animation.value)),
          child: Transform.scale(
            scale: animation.value,
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _getIndicatorColor(index, context),
                boxShadow: index == 0
                    ? [
                        BoxShadow(
                          color: context.primaryColor.withAlpha((77 * animation.value).toInt()),
                          blurRadius: 8,
                          spreadRadius: 2,
                        )
                      ]
                    : null,
              ),
              child: DotIndicator(
                size: 40,
                color: _getIndicatorColor(index, context),
                child: Icon(data['icon'], color: Colors.white, size: 20),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedConnector(int index, BuildContext context) {
    if (index == _itemCount - 1) return const SizedBox.shrink();

    final intervalStart = (index * (1.0 / _itemCount)) + (0.5 / _itemCount);
    final intervalEnd = intervalStart + (0.5 / _itemCount);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final animation = CurvedAnimation(
          parent: _animationController,
          curve: Interval(
            intervalStart,
            intervalEnd,
            curve: Curves.easeInOut,
          ),
        );

        return Opacity(
          opacity: animation.value,
          child: SolidLineConnector(
            color: context.primaryColor.withAlpha(100),
            thickness: 10,
            space: 8,
          ),
        );
      },
    );
  }
}

Map<String, dynamic> _getTimelineData(int index) {
  return [
    {
      'title': 'اليوم',
      'description': 'افتح جميع ميزات التطبيق مثل حساب\nالسعرات بتقنية الذكاء الاصطناعي والمزيد.',
      'icon': Icons.lock_outline,
    },
    {
      'title': 'بعد يومين - تذكير',
      'description': 'سنرسل لك تذكيرًا بأن التجربة المجانية\nستنتهي قريبًا.',
      'icon': Icons.notifications_outlined,
    },
    {
      'title': 'بعد 3 أيام - بدء الاشتراك',
      'description': 'سيتم خصم الرسوم في 28 مايو 2025 ما لم\nتقم بإلغاء الاشتراك قبل ذلك.',
      'icon': Icons.workspace_premium_outlined,
    },
  ][index];
}
