import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/target_weight_selector.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TargetWeightContent extends StatefulWidget {
  const TargetWeightContent({super.key});

  @override
  State<TargetWeightContent> createState() => _TargetWeightContentState();
}

class _TargetWeightContentState extends State<TargetWeightContent> {
  @override
  void initState() {
    super.initState();
    context.read<OnboardingBloc>().add(RegisterScreenValidation((state) => state.targetWeight != null));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        final isWeightLoss = state.goal == Goal.weightLoss;
        final currentWeight = state.weight ?? 70;

        final defaultTargetWeight = isWeightLoss ? currentWeight - 5 : currentWeight + 5;
        final initialTargetWeight = state.targetWeight ?? defaultTargetWeight;

        return OnboardingScreenTemplate(
          title: isWeightLoss
              ? LocaleKeys.onboarding_target_weight_loss.tr()
              : LocaleKeys.onboarding_target_weight_gain.tr(),
          description: LocaleKeys.onboarding_we_will_use_this_information_to_personalize_your_plan.tr(),
          contentWidgets: [
            const SizedBox(height: 80),
            Text(
              isWeightLoss
                  ? LocaleKeys.onboarding_target_weight_loss_description.tr()
                  : LocaleKeys.onboarding_target_weight_gain_description.tr(),
              style: context.textTheme.bodyLarge!.copyWith(color: context.onSecondary.withAlpha(178)),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            TargetWeightSelector(
              initialWeight: initialTargetWeight.toDouble(),
              currentWeight: currentWeight,
              isWeightLoss: isWeightLoss,
              onWeightChanged: (weight) => context.read<OnboardingBloc>().add(UpdateTargetWeight(weight)),
            ),
          ],
        );
      },
    );
  }
}
