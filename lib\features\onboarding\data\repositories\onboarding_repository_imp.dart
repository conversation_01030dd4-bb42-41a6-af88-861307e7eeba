import 'package:cal/core/local_models/user_model/user_model.dart';
import 'package:cal/core/network/exceptions.dart';
import 'package:cal/features/onboarding/data/datasource/onboarding_remote_datasource.dart';
import 'package:cal/features/onboarding/domain/repositories/onboarding_repository.dart';
import 'package:cal/features/onboarding/domain/usecases/submit_onboarding_usecase.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/datasources/user_local_data_source.dart';

@LazySingleton(as: OnboardingRepository)
class OnboardingRepositoryImp implements OnboardingRepository {
  final OnboardingRemoteDatasource onboardingRemoteDatasource;
  final UserLocalDataSource userLocalDataSource;

  OnboardingRepositoryImp({required this.onboardingRemoteDatasource, required this.userLocalDataSource});

  @override
  Future<Either<Failure, void>> submitOnboarding({required SubmitOnboardingParams onboardingParams}) {
    return onboardingRemoteDatasource.submitOnboarding(submitOnboardingParams: onboardingParams);
  }

  @override
  Future<void> clearUserData() {
    return userLocalDataSource.clearUser();
  }

  @override
  Future<void> deleteUserData(UserModel user) {
    return userLocalDataSource.deleteUser(user);
  }

  @override
  Future<UserModel?> getUserData(UserModel user) {
    return userLocalDataSource.getUserData();
  }

  @override
  Future<void> saveUserData({required UserModel user}) {
    return userLocalDataSource.saveUserData(user);
  }

  @override
  Future<void> updateUserData(UserModel user) {
    return userLocalDataSource.updateUser(user);
  }
}
