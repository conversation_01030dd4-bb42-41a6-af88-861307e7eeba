import 'dart:async';

import 'package:cal/features/subscriptions/data/datasources/subscription_local_datasource.dart';
import 'package:cal/features/subscriptions/data/datasources/subscription_remote_datasource.dart';
import 'package:cal/features/subscriptions/domain/entities/subscription_plan_entity.dart';
import 'package:cal/features/subscriptions/domain/repositories/subscription_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:in_app_purchase/in_app_purchase.dart' as iap;

class SubscriptionRepositoryImpl implements SubscriptionRepository {
  final SubscriptionLocalDataSource localDataSource;
  final SubscriptionRemoteDataSource remoteDataSource;

  SubscriptionRepositoryImpl({
    required this.localDataSource,
    required this.remoteDataSource,
  });

  @override
  Future<Either<String, List<SubscriptionPlanEntity>>> getSubscriptionPlans() async {
    final storeAvailableResult = await remoteDataSource.isStoreAvailable();
    
    return storeAvailableResult.fold(
      (error) => Left(error),
      (isAvailable) async {
        if (!isAvailable) {
          final mockPlansResult = await localDataSource.getMockSubscriptionPlans();
          return mockPlansResult.fold(
            (error) => Left(error),
            (mockPlans) => Right(mockPlans.map((model) => model.toEntity()).toList()),
          );
        }
        
        final plansResult = await remoteDataSource.getSubscriptionPlans();
        return plansResult.fold(
          (error) => Left(error),
          (plans) => Right(plans.map((model) => model.toEntity()).toList()),
        );
      },
    );
  }

  @override
  Future<Either<String, void>> purchaseSubscription(SubscriptionPlanEntity plan) async {
    return await remoteDataSource.purchaseSubscription(plan);
  }

  @override
  Future<Either<String, void>> restorePurchases() async {
    return await remoteDataSource.restorePurchases();
  }

  @override
  Future<Either<String, String>> verifyPurchase(iap.PurchaseDetails purchaseDetails) async {
    final productId = purchaseDetails.productID;
    
    final saveResult = await localDataSource.saveActiveSubscriptionId(productId);
    
    return saveResult.fold(
      (error) => Left(error),
      (_) => Right(productId),
    );
  }

  @override
  Future<Either<String, String?>> getActiveSubscriptionId() async {
    return await localDataSource.getActiveSubscriptionId();
  }

  @override
  Stream<iap.PurchaseDetails> get purchaseUpdates => remoteDataSource.purchaseUpdates;

  @override
  Future<Either<String, bool>> isStoreAvailable() async {
    return await remoteDataSource.isStoreAvailable();
  }
}
