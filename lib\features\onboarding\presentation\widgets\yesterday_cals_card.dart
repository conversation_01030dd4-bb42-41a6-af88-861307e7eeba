import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class YesterdayCalsCard extends StatelessWidget {
  final String description;
  final String? buttonTitle;
  final String? icon;
  final bool addCals;
  final double progress;
  final void Function()? onTap;
  final Duration animationDuration;

  const YesterdayCalsCard({
    super.key,
    required this.description,
    this.buttonTitle,
    this.icon,
    required this.progress,
    this.onTap,
    this.animationDuration = const Duration(milliseconds: 800),
    this.addCals = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppGestureDetector(
      onTap: onTap,
      child: Container(
        width: 155,
        // height: context.screenHeight * .21,
        decoration: BoxDecoration(
          color: context.colorScheme.onPrimary,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha((0.2 * 255).round()),
              blurRadius: 5,
            ),
          ],
        ),
        // margin: const EdgeInsets.all(5),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              // mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 14.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // const SizedBox(width: 8),
                      const AppImage.asset(Assets.imagesCals),
                      // const SizedBox(width: 8),
                      Text(
                        description,
                        style: context.textTheme.labelLarge!
                            .copyWith(color: context.primaryColor, fontWeight: FontWeight.bold),
                      ),
                      Text(
                        " ",
                        style: context.textTheme.labelLarge!
                            .copyWith(color: context.primaryColor, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                Divider(color: context.primaryColor),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      "500/",
                      style: context.textTheme.labelSmall!.copyWith(color: Colors.black, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      "350",
                      style: context.textTheme.labelLarge!.copyWith(color: Colors.black, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                const SizedBox(height: 9),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 14.0),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      TweenAnimationBuilder<double>(
                        duration: animationDuration,
                        curve: Curves.easeInOut,
                        tween: Tween<double>(
                          begin: 0,
                          end: progress,
                        ),
                        builder: (context, value, _) {
                          return SizedBox(
                            width: context.screenWidth * .22,
                            height: context.screenWidth * .22,
                            child: CircularProgressIndicator(
                              value: value,
                              strokeWidth: 5,
                              backgroundColor: context.onSecondary.withAlpha(15),
                              valueColor: AlwaysStoppedAnimation<Color>(context.primaryColor),
                            ),
                          );
                        },
                      ),

                      // Icon in the center
                      Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          color: context.onSecondary.withAlpha(15),
                          shape: BoxShape.circle,
                        ),
                        child: const AppImage.asset(Assets.imagesCals),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 30),
              ],
            ),
            Positioned(
              left: 5,
              bottom: 70,
              child: Container(
                  // width: 59,
                  // height: 25,
                  decoration: BoxDecoration(color: context.primaryColor, borderRadius: BorderRadius.circular(10)),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 3.0, horizontal: 3),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          LocaleKeys.onboarding_left_cals.tr(),
                          style: context.textTheme.labelSmall!
                              .copyWith(color: context.onPrimaryColor, fontWeight: FontWeight.bold, fontSize: 10),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (addCals != false)
                              Text(
                                "150+",
                                style: context.textTheme.labelSmall!
                                    .copyWith(color: Colors.black, fontWeight: FontWeight.bold, fontSize: 10),
                              ),
                            Text(
                              "150",
                              style: context.textTheme.labelSmall!
                                  .copyWith(color: context.onPrimaryColor, fontWeight: FontWeight.bold, fontSize: 10),
                            ),
                          ],
                        ),
                      ],
                    ),
                  )),
            ),
          ],
        ),
      ),
    );
  }
}
