import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/weight_change_rate_selector.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class WeightChangeRateContent extends StatefulWidget {
  const WeightChangeRateContent({super.key});

  @override
  State<WeightChangeRateContent> createState() => _WeightChangeRateContentState();
}

class _WeightChangeRateContentState extends State<WeightChangeRateContent> {
  @override
  void initState() {
    super.initState();
    context.read<OnboardingBloc>().add(RegisterScreenValidation((state) => state.weightChangeRate != null));

    context.read<OnboardingBloc>().add(const UpdateWeightChangeRate(0.5));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        final bool isWeightLoss = state.goal == Goal.weightLoss;
        final String title = isWeightLoss
            ? LocaleKeys.onboarding_weekly_weight_loss_rate.tr()
            : LocaleKeys.onboarding_weekly_weight_gain_rate.tr();
        final String description =
            isWeightLoss ? LocaleKeys.onboarding_weight_loss_speed.tr() : LocaleKeys.onboarding_weight_gain_speed.tr();

        return OnboardingScreenTemplate(
          title: title,
          // description: description,
          contentWidgets: [
            const SizedBox(height: 118),
            Text(
              description,
              textAlign: TextAlign.center,
              style: context.textTheme.bodyLarge!.copyWith(
                fontWeight: FontWeight.w400,
                color: context.onSecondary.withAlpha(178),
              ),
            ),
            const SizedBox(height: 15),
            WeightChangeRateSelector(
              initialRate: state.weightChangeRate ?? 0.5,
              onRateChanged: (rate) => context.read<OnboardingBloc>().add(UpdateWeightChangeRate(rate)),
              goal: state.goal!,
            ),
          ],
        );
      },
    );
  }
}
