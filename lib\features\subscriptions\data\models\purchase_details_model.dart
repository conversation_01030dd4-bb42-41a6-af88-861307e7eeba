import 'package:cal/features/subscriptions/enums.dart';
import 'package:equatable/equatable.dart';
import 'package:in_app_purchase/in_app_purchase.dart' as iap;

class PurchaseDetailsModel extends Equatable {
  final String productId;
  final String transactionId;
  final DateTime purchaseDate;
  final SubscriptionPurchaseStatus status;
  final String? error;

  const PurchaseDetailsModel({
    required this.productId,
    required this.transactionId,
    required this.purchaseDate,
    required this.status,
    this.error,
  });

  factory PurchaseDetailsModel.fromPurchaseDetails(iap.PurchaseDetails details) {
    return PurchaseDetailsModel(
      productId: details.productID,
      transactionId: details.purchaseID ?? '',
      purchaseDate: DateTime.now(), // PurchaseDetails doesn't provide purchase date
      status: _mapStatus(details.status),
      error: details.error?.message,
    );
  }

  static SubscriptionPurchaseStatus _mapStatus(iap.PurchaseStatus? status) {
    switch (status) {
      case iap.PurchaseStatus.pending:
        return SubscriptionPurchaseStatus.loading;
      case iap.PurchaseStatus.purchased:
        return SubscriptionPurchaseStatus.purchased;
      case iap.PurchaseStatus.error:
        return SubscriptionPurchaseStatus.error;
      default:
        return SubscriptionPurchaseStatus.initial;
    }
  }

  @override
  List<Object?> get props => [productId, transactionId, purchaseDate, status, error];
}
