part of 'onboarding_bloc.dart';

enum OnboardingStatus {
  initial,
  loading,
  processing,
  success,
  error,
}

class OnboardingState extends Equatable {
  final OnboardingStatus status;
  final String? errorMessage;
  final bool Function(OnboardingState)? currentScreenValidation;

  // User data fields
  final Gender? gender;
  final ExerciseFrequency? exerciseFrequency;
  final HeardAboutUs? whereDidYouHearOfUs;
  final Choice? hasUsedOtherApps;
  final int? height;
  final int? weight;
  final int? birthDay;
  final int? birthMonth;
  final int? birthYear;
  final Goal? goal;
  final WhatMakesYouDoesnotCommit? whatMakesYouDoesnotCommit;
  final Diet? diet;
  final Achieve? achieve;

  final double? targetWeight;
  final double? weightChangeRate;

  final bool? addCalsToTargetAgain;
  final bool? addYesterdayCalsToTarget;
  final bool? shouldShowInSettingsScreens;
  final bool? isFromSettings;
  final bool isButtonActive;

  final String? firstMeal;
  final String? secondMeal;
  final String? thirdMeal;

  // Calculation results
  final int? cals;
  final int? fat;
  final int? protein;
  final int? carbs;

  // Flow control fields
  final List<String> flowScreenIds;
  final int flowIndex;

  const OnboardingState({
    this.firstMeal,
    this.secondMeal,
    this.thirdMeal,
    this.addCalsToTargetAgain,
    this.addYesterdayCalsToTarget,
    this.status = OnboardingStatus.initial,
    this.errorMessage,
    this.currentScreenValidation,
    this.gender,
    this.exerciseFrequency,
    this.whereDidYouHearOfUs,
    this.hasUsedOtherApps,
    this.height = 170,
    this.weight = 70,
    this.birthDay = 1,
    this.birthMonth = 1,
    this.birthYear = 2000,
    this.goal,
    this.achieve,
    this.whatMakesYouDoesnotCommit,
    this.diet,
    this.targetWeight,
    this.weightChangeRate,
    this.cals,
    this.fat,
    this.protein,
    this.carbs,
    this.flowScreenIds = const [],
    this.flowIndex = 0,
    this.shouldShowInSettingsScreens,
    this.isFromSettings,
    this.isButtonActive = false,
  });

  // Helper methods for flow navigation
  String? get nextScreenId => flowIndex < flowScreenIds.length - 1 ? flowScreenIds[flowIndex + 1] : null;
  String? get previousScreenId => flowIndex > 0 ? flowScreenIds[flowIndex - 1] : null;
  String get currentScreenId => flowScreenIds.isNotEmpty ? flowScreenIds[flowIndex] : '';
  int get totalScreens => flowScreenIds.length;
  double get progressPercentage => totalScreens > 0 ? flowIndex / (totalScreens - 1) : 0.0;

  OnboardingState copyWith({
    String? firstMeal,
    String? secondMeal,
    String? thirdMeal,
    bool? addCalsToTargetAgain,
    bool? addYesterdayCalsToTarget,
    OnboardingStatus? status,
    String? errorMessage,
    bool Function(OnboardingState)? currentScreenValidation,
    Gender? gender,
    ExerciseFrequency? exerciseFrequency,
    HeardAboutUs? whereDidYouHearOfUs,
    Choice? hasUsedOtherApps,
    int? height,
    int? weight,
    int? birthDay,
    int? birthMonth,
    int? birthYear,
    Goal? goal,
    WhatMakesYouDoesnotCommit? whatMakesYouDoesnotCommit,
    Diet? diet,
    Achieve? achieve,
    double? targetWeight,
    double? weightChangeRate,
    int? cals,
    int? fat,
    int? protein,
    int? carbs,
    List<String>? flowScreenIds,
    int? flowIndex,
    bool? shouldShowInSettingsScreens,
    bool? isFromSettings,
    bool? isButtonActive,
  }) {
    return OnboardingState(
      addCalsToTargetAgain: addCalsToTargetAgain ?? this.addCalsToTargetAgain,
      addYesterdayCalsToTarget: addYesterdayCalsToTarget ?? this.addYesterdayCalsToTarget,
      firstMeal: firstMeal ?? this.firstMeal,
      secondMeal: secondMeal ?? this.secondMeal,
      thirdMeal: thirdMeal ?? this.thirdMeal,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      currentScreenValidation: currentScreenValidation ?? this.currentScreenValidation,
      gender: gender ?? this.gender,
      exerciseFrequency: exerciseFrequency ?? this.exerciseFrequency,
      whereDidYouHearOfUs: whereDidYouHearOfUs ?? this.whereDidYouHearOfUs,
      hasUsedOtherApps: hasUsedOtherApps ?? this.hasUsedOtherApps,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      birthDay: birthDay ?? this.birthDay,
      birthMonth: birthMonth ?? this.birthMonth,
      birthYear: birthYear ?? this.birthYear,
      goal: goal ?? this.goal,
      achieve: achieve ?? this.achieve,
      whatMakesYouDoesnotCommit: whatMakesYouDoesnotCommit ?? this.whatMakesYouDoesnotCommit,
      diet: diet ?? this.diet,
      targetWeight: targetWeight ?? this.targetWeight,
      weightChangeRate: weightChangeRate ?? this.weightChangeRate,
      cals: cals ?? this.cals,
      fat: fat ?? this.fat,
      protein: protein ?? this.protein,
      carbs: carbs ?? this.carbs,
      flowScreenIds: flowScreenIds ?? this.flowScreenIds,
      flowIndex: flowIndex ?? this.flowIndex,
      shouldShowInSettingsScreens: shouldShowInSettingsScreens,
      isFromSettings: isFromSettings,
      isButtonActive: isButtonActive ?? this.isButtonActive,
    );
  }

  bool isCurrentScreenValid() {
    return currentScreenValidation != null ? currentScreenValidation!(this) : true;
  }

  @override
  List<Object?> get props => [
        status,
        errorMessage,
        currentScreenValidation,
        gender,
        exerciseFrequency,
        whereDidYouHearOfUs,
        hasUsedOtherApps,
        height,
        weight,
        birthDay,
        birthMonth,
        birthYear,
        goal,
        achieve,
        whatMakesYouDoesnotCommit,
        targetWeight,
        weightChangeRate,
        cals,
        fat,
        protein,
        carbs,
        flowScreenIds,
        flowIndex,
        firstMeal,
        secondMeal,
        thirdMeal,
        addYesterdayCalsToTarget,
        addCalsToTargetAgain,
        diet,
        shouldShowInSettingsScreens,
        isButtonActive,
        isCurrentScreenValid(),
      ];
}
