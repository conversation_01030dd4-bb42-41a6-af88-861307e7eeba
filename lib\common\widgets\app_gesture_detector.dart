import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppGestureDetector extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;

  const AppGestureDetector({
    super.key,
    required this.child,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      key: key,
      onTap: () {
        if (onTap != null) {
          HapticFeedback.selectionClick();

          onTap!();
        }
      },
      child: child,
    );
  }
}
