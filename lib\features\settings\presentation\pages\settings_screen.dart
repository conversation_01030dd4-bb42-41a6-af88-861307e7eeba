import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/theme/text_theme.dart';
import 'package:cal/common/utils/shared_preferences_helper.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/core/datasources/user_local_data_source.dart';
import 'package:cal/core/local_models/user_model/user_model.dart';
import 'package:cal/features/onboarding/presentation/pages/welcome_screen.dart';
import 'package:cal/features/onboarding/presentation/widgets/language_button.dart';
import 'package:cal/features/settings/presentation/pages/personal_details_screen.dart';
import 'package:cal/features/settings/presentation/pages/weight_history_screen.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:isar/isar.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/di/injection.dart';
import 'ingredient_settings_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool addCalories = true;
  bool aboutCalories = true;
  bool disableCalories = false;

  UserModel? user;

  @override
  void initState() {
    super.initState();
    fetchUserData();
  }

  fetchUserData() async {
    user = await UserLocalDataSource(getIt<Isar>()).getUserData();
    setState(() {});
  }

  int calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;

    if (now.month < birthDate.month || (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }

    return age;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        forceMaterialTransparency: true,
        elevation: 0,
        automaticallyImplyLeading: false,
        titleSpacing: 17,
        title: Text(
          LocaleKeys.settings_settings.tr(),
          style: context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w700),
          textAlign: TextAlign.start,
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsetsDirectional.only(end: 16, start: 16, top: 15, bottom: 100),
        child: Column(
          children: [
            // _buildSettingsCard(
            //   children: [
            //     user == null
            //         ? const SizedBox.shrink()
            //         : _buildSettingsTile(
            //             title: 'فتوح',
            //             subtitle: '${calculateAge(user!.birthDate!)} سنة',
            //             leadingIcon: const Icon(Icons.person),
            //             isItProfileLeading: true,
            //             onTap: () {},
            //           ),
            //   ],
            // ),
            const SizedBox(height: 16),
            _buildSettingsCard(
              children: [
                // _buildSettingsTile(
                //   title: LocaleKeys.settings_personal_details.tr(),
                //   leadingIcon: const AppImage.asset(Assets.settingsDetails, size: 23),
                //   showDivider: true,
                //   onTap: () {
                //
                //   },
                // ),
                _buildSettingsTile(
                  title: LocaleKeys.settings_customize_food_items.tr(),
                  leadingIcon: const AppImage.asset(Assets.settingsAdjust, size: 22),
                  showDivider: true,
                  onTap: () {
                    context.push(const IngredientSettingsScreen());
                  },
                ),
                _buildSettingsTile(
                  title: LocaleKeys.settings_nutrition_and_weight.tr(),
                  leadingIcon: const AppImage.asset(Assets.settingsFlag, size: 25),
                  showDivider: true,
                  onTap: () {
                    context.push(const PersonalDetailsScreen());
                  },
                ),
                _buildSettingsTile(
                  title: LocaleKeys.settings_weight_log.tr(),
                  leadingIcon: const AppImage.asset(Assets.settingsRecord, size: 25),
                  showDivider: true,
                  onTap: () {
                    context.push(const WeightHistoryScreen());
                  },
                ),
                _buildSettingsTile(
                  title: LocaleKeys.settings_language.tr(),
                  leadingIcon: const AppImage.asset(Assets.settingsLanguage, size: 25),
                  onTap: () => LangButton.showLanguageDialog(context),
                ),
              ],
            ),
            // const SizedBox(height: 18),
            // _buildSettingsCard(
            //   children: [
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_Preferences.tr(),
            //       leadingIcon: const AppImage.asset(Assets.settingsSwitch, size: 25),
            //       showDivider: true,
            //       onTap: () {},
            //     ),
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_Add_the_calories_burned.tr(),
            //       subtitle: LocaleKeys.settings_Add_the_calories_burned_back_to_the_daily_goal.tr(),
            //       trailing: CustomSwitch(
            //         value: addCalories,
            //         onChanged: (val) {
            //           setState(() {
            //             addCalories = val;
            //           });
            //         },
            //       ),
            //       onTap: () {},
            //     ),
            //     // _buildSettingsTile(
            //     //   title: LocaleKeys.settings_about_calories.tr(),
            //     //   subtitle: LocaleKeys.settings_Add_up_to_200_calories_from_yesterday_to_todays_daily_goal.tr(),
            //     //   trailing: CustomSwitch(
            //     //     value: aboutCalories,
            //     //     onChanged: (val) {
            //     //       setState(() {
            //     //         aboutCalories = val;
            //     //       });
            //     //     },
            //     //   ),
            //     //   onTap: () {},
            //     // ),
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_Disable_nutrien_recalculation.tr(),
            //       subtitle: LocaleKeys.settings_Enable_this_option_to_manually_specify_calories_and_nutrients_when_editing_food.tr(),
            //       trailing: CustomSwitch(
            //         value: disableCalories,
            //         onChanged: (val) {
            //           setState(() {
            //             disableCalories = val;
            //           });
            //         },
            //       ),
            //       onTap: () {},
            //     ),
            //   ],
            // ),
            const SizedBox(height: 18),
            _buildSettingsCard(
              children: [
                // _buildSettingsTile(
                //   title: LocaleKeys.settings_Terms_and_conditions.tr(),
                //   leadingIcon: const AppImage.asset(Assets.settingsDocument, size: 25),
                //   showDivider: true,
                //   onTap: () {},
                // ),
                _buildSettingsTile(
                  title: LocaleKeys.settings_Privacy_Policy.tr(),
                  leadingIcon: const AppImage.asset(Assets.settingsPolicy, size: 25),
                  showDivider: true,
                  onTap: () async {
                    final url = Uri.parse('https://api.drmicheladib.com/orange-ai/privacy-policy');
                    if (await canLaunchUrl(url)) {
                      await launchUrl(url, mode: LaunchMode.externalApplication);
                    } else {
                      debugPrint('Could not launch $url');
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 18),
            _buildSettingsCard(
              children: [
                _buildSettingsTile(
                  title: LocaleKeys.settings_delete_account.tr(),
                  leadingIcon: const AppImage.asset(Assets.settingsDeleteUser, size: 25),
                  showDivider: true,
                  onTap: () async {
                    final bool? confirmed = await showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: Text("Confirm Account Deletion?", style: context.textTheme.bodyLarge),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(false),
                              child: Text(LocaleKeys.common_cancel.tr(), style: context.textTheme.bodyLarge),
                            ),
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(true),
                              child: Text(LocaleKeys.common_confirm.tr(),
                                  style: context.textTheme.bodyLarge!.copyWith(color: context.primaryColor)),
                            ),
                          ],
                        );
                      },
                    );
                    if (confirmed == true) {
                      await ShPH.clearData();
                      await getIt<Isar>().writeTxn(() async {
                        await getIt<Isar>().clear();
                      });
                      if (context.mounted) context.pushAndRemoveUntil(const WelcomeScreen());
                    }
                  },
                ),
                _buildSettingsTile(
                  title: LocaleKeys.settings_log_out.tr(),
                  leadingIcon: const AppImage.asset(Assets.settingsExit, size: 25),
                  showDivider: true,
                  onTap: () async {
                    final bool? confirmed = await showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: Text(LocaleKeys.common_do_u_want_to_logout.tr(), style: context.textTheme.bodyLarge),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(false),
                              child: Text(LocaleKeys.common_cancel.tr(), style: context.textTheme.bodyLarge),
                            ),
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(true),
                              child: Text(LocaleKeys.common_confirm.tr(),
                                  style: context.textTheme.bodyLarge!.copyWith(color: context.primaryColor)),
                            ),
                          ],
                        );
                      },
                    );
                    if (confirmed == true) {
                      await ShPH.clearData();
                      await getIt<Isar>().writeTxn(() async {
                        await getIt<Isar>().clear();
                      });
                      if (context.mounted) context.pushAndRemoveUntil(const WelcomeScreen());
                    }
                  },
                ),
              ],
            ),
            // const SizedBox(height: 16),
            // _buildSettingsCard(
            //   children: [
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_personal_details.tr(),
            //       leadingIcon: const AppImage.asset(Assets.settingsDetails, size: 23),
            //       showDivider: true,
            //       onTap: () {},
            //     ),
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_customize_food_items.tr(),
            //       leadingIcon: const AppImage.asset(Assets.settingsAdjust, size: 22),
            //       showDivider: true,
            //       onTap: () {},
            //     ),
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_nutrition_and_weight.tr(),
            //       leadingIcon: const AppImage.asset(Assets.settingsFlag, size: 25),
            //       showDivider: true,
            //       onTap: () {},
            //     ),
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_weight_log.tr(),
            //       leadingIcon: const AppImage.asset(Assets.settingsRecord, size: 25),
            //       showDivider: true,
            //       onTap: () {},
            //     ),
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_language.tr(),
            //       leadingIcon: const AppImage.asset(Assets.settingsLanguage, size: 25),
            //       onTap: () {},
            //     ),
            //   ],
            // ),
            // const SizedBox(height: 18),
            // _buildSettingsCard(
            //   children: [
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_Preferences.tr(),
            //       leadingIcon: const AppImage.asset(Assets.settingsSwitch, size: 25),
            //       showDivider: true,
            //       onTap: () {},
            //     ),
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_Add_the_calories_burned.tr(),
            //       subtitle: LocaleKeys.settings_Add_the_calories_burned_back_to_the_daily_goal.tr(),
            //       trailing: CustomSwitch(
            //         value: addCalories,
            //         onChanged: (val) {
            //           setState(() {
            //             addCalories = val;
            //           });
            //         },
            //       ),
            //       onTap: () {},
            //     ),
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_about_calories.tr(),
            //       subtitle: LocaleKeys.settings_Add_up_to_200_calories_from_yesterday_to_todays_daily_goal.tr(),
            //       trailing: CustomSwitch(
            //         value: aboutCalories,
            //         onChanged: (val) {
            //           setState(() {
            //             aboutCalories = val;
            //           });
            //         },
            //       ),
            //       onTap: () {},
            //     ),
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_Disable_nutrien_recalculation.tr(),
            //       subtitle: LocaleKeys.settings_Enable_this_option_to_manually_specify_calories_and_nutrients_when_editing_food.tr(),
            //       trailing: CustomSwitch(
            //         value: disableCalories,
            //         onChanged: (val) {
            //           setState(() {
            //             disableCalories = val;
            //           });
            //         },
            //       ),
            //       onTap: () {},
            //     ),
            //   ],
            // ),
            // const SizedBox(height: 18),
            // _buildSettingsCard(
            //   children: [
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_Terms_and_conditions.tr(),
            //       leadingIcon: const AppImage.asset(Assets.settingsDocument, size: 25),
            //       showDivider: true,
            //       onTap: () {},
            //     ),
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_Privacy_Policy.tr(),
            //       leadingIcon: const AppImage.asset(Assets.settingsPolicy, size: 25),
            //       showDivider: true,
            //       onTap: () {},
            //     ),
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_delete_account.tr(),
            //       leadingIcon: const AppImage.asset(Assets.settingsDeleteUser, size: 25),
            //       showDivider: true,
            //       onTap: () {},
            //     ),
            //   ],
            // ),
            // const SizedBox(height: 18),
            // _buildSettingsCard(
            //   children: [
            //     _buildSettingsTile(
            //       title: LocaleKeys.settings_log_out.tr(),
            //       leadingIcon: const AppImage.asset(Assets.settingsExit, size: 25),
            //       showDivider: true,
            //       onTap: () {},
            //     ),
            //   ],
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsCard({required List<Widget> children}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(37),
            blurRadius: 15,
            spreadRadius: 2,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildSettingsTile({
    required String title,
    String? subtitle,
    Widget? leadingIcon,
    Widget? trailing,
    required VoidCallback onTap,
    bool isItProfileLeading = false,
    bool showDivider = false,
  }) {
    return Column(
      children: [
        ListTile(
          contentPadding: leadingIcon == null ? const EdgeInsets.only(right: 27, left: 27) : null,
          title: Text(title, style: GoogleFonts.tajawal(fontWeight: FontWeight.bold, color: context.onSecondary, fontSize: 15)),
          subtitle: subtitle != null
              ? ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 140), // adjust width as needed
                  child: Text(
                    subtitle,
                    style: textTheme.labelMedium,
                    maxLines: 3, // Optional: limit max lines
                    overflow: TextOverflow.ellipsis, // Optional: ellipsis after max lines
                  ),
                )
              : null,
          leading: leadingIcon != null
              ? Container(
                  padding: isItProfileLeading ? const EdgeInsets.all(8) : const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: isItProfileLeading ? Colors.grey.withAlpha(25) : null,
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: leadingIcon,
                )
              : null,
          trailing: Padding(
            padding: const EdgeInsets.only(left: 12.0),
            child: trailing,
          ),
          onTap: onTap,
        ),
        if (showDivider)
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 12),
            child: Divider(
              height: 0.2,
              thickness: 1.3,
              color: Color.fromARGB(89, 158, 158, 158),
            ),
          ),
      ],
    );
  }
}
