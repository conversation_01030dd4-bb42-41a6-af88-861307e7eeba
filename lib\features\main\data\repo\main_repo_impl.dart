import 'package:cal/core/local_models/streak_model/streak_model.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/datasources/streak_local_data_source.dart';
import '../../domain/repo/main_repo.dart';

@Injectable(as: MainRepo)
class MainRepoImpl implements MainRepo {

  final StreakLocalDataSource localDataSource;

  MainRepoImpl({required this.localDataSource});

  @override
  Future<List<StreakModel>> getStreaks(locale) {
    return localDataSource.getStreak(locale);
  }

  @override
  Future<void> saveStreak(StreakModel streak) async {
    localDataSource.saveStreak(streak);
  }

  @override
  Future<int> getStreaksNumber() async {
    return localDataSource.getStreaksNumber();
  }
}