import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/meals_time_card.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Birth Date Screen
class MealsTimeContent extends StatefulWidget {
  const MealsTimeContent({super.key});

  @override
  State<MealsTimeContent> createState() => _BirthDateContentState();
}

class _BirthDateContentState extends State<MealsTimeContent> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_do_you_eat_all_meals_same_time.tr(),
          centerContent: true,
          contentWidgets: [
            MealsTimeCard(
              title: state.firstMeal ?? LocaleKeys.onboarding_meal_one.tr(),
              onTimeSelected: (time) {
                context.read<OnboardingBloc>().add(UpdateMealtime(firstMeal: time));
              },
            ),
            const SizedBox(height: 16),
            MealsTimeCard(
              title: state.secondMeal ?? LocaleKeys.onboarding_meal_two.tr(),
              onTimeSelected: (time) {
                context.read<OnboardingBloc>().add(UpdateMealtime(secondMeal: time));
              },
            ),
            const SizedBox(height: 16),
            MealsTimeCard(
              title: state.thirdMeal ?? LocaleKeys.onboarding_meal_three.tr(),
              onTimeSelected: (time) {
                context.read<OnboardingBloc>().add(UpdateMealtime(thirdMeal: time));
              },
            ),
          ],
        );
      },
    );
  }
}
