List<RemoteFoodDatabaseModel> remoteFoodDatabaseModelFromJson(str) => List<RemoteFoodDatabaseModel>.from((str).map((x) => RemoteFoodDatabaseModel.fromJson(x)));

class RemoteFoodDatabaseModel {
  final String foodId;
  final String foodName;
  final FoodDescription foodDescription;

  RemoteFoodDatabaseModel({
    required this.foodId,
    required this.foodName,
    required this.foodDescription,
  });

  factory RemoteFoodDatabaseModel.fromJson(Map<String, dynamic> json) {
    return RemoteFoodDatabaseModel(
      foodId: json['food_id'] ?? '',
      foodName: json['food_name'] ?? '',
      foodDescription: FoodDescription.fromJson(json['food_description']),
    );
  }

  Map<String, dynamic> toJson() => {
        'food_id': foodId,
        'food_name': foodName,
        'food_description': foodDescription.toJson(),
      };
}

class FoodDescription {
  final num calories;
  final num fat;
  final num carbs;
  final num protein;
  final List<String>? ingredients;

  FoodDescription({
    required this.calories,
    required this.fat,
    required this.carbs,
    required this.protein,
    this.ingredients,
  });

  factory FoodDescription.fromJson(Map<String, dynamic> json) {
    return FoodDescription(
      calories: json['Calories'],
      fat: json['Fat'],
      carbs: json['Carbs'],
      protein: json['Protein'],
      ingredients: json['ingredients'] == null ? [] : List<String>.from(json['ingredients']),
    );
  }

  Map<String, dynamic> toJson() => {
        'Calories': calories,
        'Fat': fat,
        'Carbs': carbs,
        'Protein': protein,
        'ingredients': ingredients,
      };
}
