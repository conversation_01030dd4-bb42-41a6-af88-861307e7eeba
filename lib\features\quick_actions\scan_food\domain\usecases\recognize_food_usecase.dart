import 'dart:io';

import 'package:cal/common/consts/typedef.dart';
import 'package:cal/core/mobile_id_helper.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/quick_actions/scan_food/domain/repositories/scan_food_repository.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class AnalyzeFoodUseCase {
  final ScanFoodRepository repository;

  AnalyzeFoodUseCase(this.repository);

  DataResponse<FoodModel> call({required AnalyzeFoodParams analyzeFoodParams}) {
    return repository.recognizeFood(analyzeFoodParams: analyzeFoodParams);
  }
}

class AnalyzeFoodParams with Params {
  final String mobileID;
  final File imageFile;

  final bool isLabel;

  AnalyzeFoodParams._({
    required this.mobileID,
    required this.imageFile,
    required this.isLabel,
  });

  static Future<AnalyzeFoodParams> create({
    required File imageFile,
    required bool isLabel,
  }) async {
    final id = await MobileIdHelper.getMobileId();
    return AnalyzeFoodParams._(
      mobileID: id,
      isLabel: isLabel,
      imageFile: imageFile,
    );
  }

  @override
  BodyMap getBody() => {
        "image": imageFile,
        if(isLabel) "prompt": prompt,
      };
}

const prompt = ''' You are an OCR and nutrition label analyzer. Analyze the uploaded image, which contains a nutrition facts label in English. Extract the nutritional values and return them in this exact JSON format:
{
  "calories": (float),
  "fats": (float),
  "carbs": (float),
  "protein": (float),
"halal": (null),
"serving": (null),
"health_score": (null),
  "english_name": "Nutrition Facts",
  "arabic_name": "جدول القيم الغذائية"
}
Make sure the response is valid JSON and the values are accurate numbers. Ignore irrelevant text or branding. ''';