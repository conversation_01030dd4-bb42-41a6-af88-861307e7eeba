import 'dart:developer';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:cal/common/consts/app_consts.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/home/<USER>/bloc/recent_activity_bloc.dart'; // Import RecentActivityBloc
import 'package:cal/features/quick_actions/scan_food/domain/usecases/recognize_food_usecase.dart';
import 'package:cal/features/quick_actions/scan_food/domain/usecases/scan_barcode_use_case.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

part 'scan_food_event.dart';

part 'scan_food_state.dart';

@injectable
class ScanFoodBloc extends Bloc<ScanFoodEvent, ScanFoodState> {
  final AnalyzeFoodUseCase analyzeFoodUseCase;
  final ImagePicker _imagePicker = ImagePicker();
  final RecentActivityBloc _recentActivityBloc;
  final ScanBarcodeUseCase scanBarcodeUseCase;

  ScanFoodBloc({
    required this.analyzeFoodUseCase,
    required this.scanBarcodeUseCase,
    required RecentActivityBloc recentActivityBloc,
  })  : _recentActivityBloc = recentActivityBloc,
        super(const ScanFoodState()) {
    on<ProcessCapturedImageEvent>(_onProcessCapturedImage);
    on<PickImageFromGalleryEvent>(_onPickImageFromGallery);
    on<RecognizeFoodEvent>(_onRecognizeFood);
    on<RetryRecognizeFoodEvent>(_onRetryRecognizeFood);
    on<RetryScanBarcodeEvent>(_onRetryScanBarcode);
    on<RetryLastScanEvent>(_onRetryLastScan);
    on<ScanBarcodeEvent>(_scanBarcode);
  }

  Future<void> _onProcessCapturedImage(ProcessCapturedImageEvent event, Emitter<ScanFoodState> emit) async {
    emit(state.copyWith(status: ScanFoodStatus.processing, capturedImage: event.imageFile));

    // Add shimmer food to RecentFoodBloc
    final shimmerFood = FoodModel(
      date: DateTime.now(),
      imagePath: event.imageFile.path,
      isLoading: true,
      tempId: DateTime.now().millisecondsSinceEpoch.toString(),
    );
    _recentActivityBloc.add(AddFood(meal: shimmerFood));

    if (event.isBarcode) {
      add(ScanBarcodeEvent(barcode: event.barcode!, imageFile: event.imageFile));
    } else {
      add(RecognizeFoodEvent(image: event.imageFile, isLabel: event.isLabel));
    }
  }

  Future<void> _onPickImageFromGallery(PickImageFromGalleryEvent event, Emitter<ScanFoodState> emit) async {
    emit(state.copyWith(status: ScanFoodStatus.processing));

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image == null) {
        emit(state.copyWith(status: ScanFoodStatus.initial));
        return;
      }

      File imageFile = File(image.path);

      emit(state.copyWith(capturedImage: imageFile));

      // Add shimmer food to RecentFoodBloc
      final shimmerFood = FoodModel(
        date: DateTime.now(),
        imagePath: imageFile.path,
        isLoading: true,
        tempId: DateTime.now().millisecondsSinceEpoch.toString(),
      );
      _recentActivityBloc.add(AddFood(meal: shimmerFood));

      add(RecognizeFoodEvent(image: imageFile, isLabel: false));
      event.context?.pop();
    } catch (e) {
      emit(state.copyWith(
        status: ScanFoodStatus.error,
        errorMessage: 'Failed to pick image from gallery: $e',
      ));
    }
  }

  Future<void> _onRecognizeFood(RecognizeFoodEvent event, Emitter<ScanFoodState> emit) async {
    emit(state.copyWith(
      status: ScanFoodStatus.processing,
      originalScanType: OriginalScanType.foodRecognition,
      originalIsLabel: event.isLabel,
    ));

    log('Processing food recognition');

    var connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult.contains(ConnectivityResult.none)) {
      emit(state.copyWith(status: ScanFoodStatus.error, errorMessage: LocaleKeys.common_no_internet_connection.tr()));
      // Update RecentFoodBloc with error status
      final errorFood = FoodModel(
        date: DateTime.now(),
        imagePath: event.image.path,
        isLoading: false,
        hasError: true,
        tempId: DateTime.now().millisecondsSinceEpoch.toString(),
      );
      _recentActivityBloc.add(UpdateFood(errorFood));
      return;
    }

    final result = await analyzeFoodUseCase(analyzeFoodParams: await AnalyzeFoodParams.create(imageFile: event.image, isLabel: event.isLabel));

    await result.fold(
      (failure) async {
        emit(state.copyWith(status: ScanFoodStatus.error, errorMessage: failure.message));
        // Update RecentFoodBloc with error status
        final errorFood = FoodModel(
          date: DateTime.now(),
          imagePath: event.image.path,
          isLoading: false,
          hasError: true,
          tempId: DateTime.now().millisecondsSinceEpoch.toString(),
        );
        log("Not loading any more - ScanBloc");
        _recentActivityBloc.add(UpdateFood(errorFood));
      },
      (foodData) async {
        final savedPath = await saveImagePermanently(event.image);
        final recognizedFood = foodData.copyWith(imagePath: savedPath, isLoading: false);

        emit(state.copyWith(status: ScanFoodStatus.success, recognizedFood: recognizedFood, isRetry: state.isRetry));
        // Update RecentFoodBloc with recognized food
        _recentActivityBloc.add(UpdateFood(recognizedFood));
      },
    );
  }

  Future<void> _scanBarcode(ScanBarcodeEvent event, Emitter<ScanFoodState> emit) async {
    emit(state.copyWith(
      status: ScanFoodStatus.processing,
      originalScanType: OriginalScanType.barcode,
      originalBarcode: event.barcode,
    ));

    log('Processing barcode scan');

    var connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult.contains(ConnectivityResult.none)) {
      emit(state.copyWith(status: ScanFoodStatus.error, errorMessage: LocaleKeys.common_no_internet_connection.tr()));
      // Update RecentFoodBloc with error status
      final errorFood = FoodModel(
        date: DateTime.now(),
        imagePath: event.imageFile.path,
        isLoading: false,
        hasError: true,
        tempId: DateTime.now().millisecondsSinceEpoch.toString(),
      );
      _recentActivityBloc.add(UpdateFood(errorFood));
      return;
    }

    final result = await scanBarcodeUseCase(ScanBarcodeParams(barcode: event.barcode));

    await result.fold(
      (failure) async {
        emit(state.copyWith(status: ScanFoodStatus.error, errorMessage: failure.message));
        // Update RecentActivityBloc with error status
        final errorFood = FoodModel(
          date: DateTime.now(),
          imagePath: event.imageFile.path,
          isLoading: false,
          hasError: true,
          tempId: DateTime.now().millisecondsSinceEpoch.toString(),
        );
        log("Not loading any more - ScanBloc");
        _recentActivityBloc.add(UpdateFood(errorFood));
      },
      (foodData) async {
        final savedPath = await saveImagePermanently(event.imageFile);
        final scannedBarcode = foodData.copyWith(imagePath: savedPath, isLoading: false);

        emit(state.copyWith(status: ScanFoodStatus.success, scanBarcode: scannedBarcode, isRetry: state.isRetry));
        // Update RecentActivityBloc with recognized food
        _recentActivityBloc.add(UpdateFood(scannedBarcode));
      },
    );
  }

  Future<void> _onRetryRecognizeFood(RetryRecognizeFoodEvent event, Emitter<ScanFoodState> emit) async {
    if (state.capturedImage != null && state.originalScanType == OriginalScanType.foodRecognition) {
      emit(state.copyWith(status: ScanFoodStatus.processing, isRetry: true));

      // Update RecentFoodBloc with processing status for retry
      final processingFood = FoodModel(
        date: DateTime.now(),
        imagePath: state.capturedImage!.path,
        isLoading: true,
        tempId: DateTime.now().millisecondsSinceEpoch.toString(),
      );
      _recentActivityBloc.add(UpdateFood(processingFood));

      add(RecognizeFoodEvent(image: state.capturedImage!, isLabel: state.originalIsLabel ?? false));
    } else {
      emit(state.copyWith(status: ScanFoodStatus.error, errorMessage: 'No image to retry or wrong scan type'));
    }
  }

  Future<void> _onRetryScanBarcode(RetryScanBarcodeEvent event, Emitter<ScanFoodState> emit) async {
    if (state.capturedImage != null && state.originalScanType == OriginalScanType.barcode && state.originalBarcode != null) {
      emit(state.copyWith(status: ScanFoodStatus.processing, isRetry: true));

      // Update RecentFoodBloc with processing status for retry
      final processingFood = FoodModel(
        date: DateTime.now(),
        imagePath: state.capturedImage!.path,
        isLoading: true,
        tempId: DateTime.now().millisecondsSinceEpoch.toString(),
      );
      _recentActivityBloc.add(UpdateFood(processingFood));

      add(ScanBarcodeEvent(barcode: state.originalBarcode!, imageFile: state.capturedImage!));
    } else {
      emit(state.copyWith(status: ScanFoodStatus.error, errorMessage: 'No image or barcode to retry or wrong scan type'));
    }
  }

  Future<void> _onRetryLastScan(RetryLastScanEvent event, Emitter<ScanFoodState> emit) async {
    // Determine which type of retry to trigger based on the original scan type
    if (state.originalScanType == OriginalScanType.barcode) {
      add(const RetryScanBarcodeEvent());
    } else if (state.originalScanType == OriginalScanType.foodRecognition) {
      add(const RetryRecognizeFoodEvent());
    } else {
      emit(state.copyWith(status: ScanFoodStatus.error, errorMessage: 'No previous scan to retry'));
    }
  }
}

Future<String> saveImagePermanently(File image) async {
  final baseDir = await getApplicationDocumentsDirectory();
  final imageDir = Directory(p.join(baseDir.path, AppConsts.appName, 'images'));

  if (!await imageDir.exists()) {
    await imageDir.create(recursive: true);
  }

  final extension = p.extension(image.path);
  final fileName = 'image_${DateTime.now().millisecondsSinceEpoch}$extension';
  final newPath = p.join(imageDir.path, fileName);

  final newImage = await File(image.path).copy(newPath);

  if (kDebugMode) {
    print('Image saved at: $newPath');
  }

  return newImage.path;
}
