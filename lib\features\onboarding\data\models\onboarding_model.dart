// To parse this JSON data, do
//
//     final onboardingModel = onboardingModelFromJson(jsonString);

import 'dart:convert';

OnboardingModel onboardingModelFromJson(str) => OnboardingModel.fromJson(str);

String onboardingModelToJson(OnboardingModel data) => json.encode(data.toJson());

class OnboardingModel {
  String? mobileId;
  String? firebaseToken;
  bool? caloriesAdded;
  int? height;
  int? weight;
  String? gender;
  String? birthdate;
  String? activity;
  String? tdeeGoal;
  double? targetWeight;
  double? weeklyTarget;
  String? diet;
  String? goal;
  bool? triedAnotherApp;
  String? hearingAboutUs;
  List<Meal>? meals;

  OnboardingModel({
    this.mobileId,
    this.firebaseToken,
    this.caloriesAdded,
    this.height,
    this.weight,
    this.gender,
    this.birthdate,
    this.activity,
    this.tdeeGoal,
    this.targetWeight,
    this.weeklyTarget,
    this.diet,
    this.goal,
    this.triedAnotherApp,
    this.hearingAboutUs,
    this.meals,
  });

  factory OnboardingModel.fromJson(Map<String, dynamic> json) => OnboardingModel(
        mobileId: json["mobile_id"],
        firebaseToken: json["firebase_token"],
        caloriesAdded: json["calories_added"],
        height: json["height"],
        weight: json["weight"],
        gender: json["gender"],
        birthdate: json["birthdate"],
        activity: json["activity"],
        tdeeGoal: json["tdee_goal"],
        targetWeight: json["target_weight"],
        weeklyTarget: json["weekly_target"],
        diet: json["diet"],
        goal: json["goal"],
        triedAnotherApp: json["tried_another_app"],
        hearingAboutUs: json["hearing_about_us"],
        meals: json["meals"] != null ? List<Meal>.from(json["meals"].map((x) => Meal.fromJson(x))) : [],
      );

  Map<String, dynamic> toJson() => {
        "mobile_id": mobileId,
        "firebase_token": firebaseToken,
        "calories_added": caloriesAdded,
        "height": height,
        "weight": weight,
        "gender": gender,
        "birthdate": birthdate,
        "activity": activity,
        "tdee_goal": tdeeGoal,
        "target_weight": targetWeight,
        "weekly_target": weeklyTarget,
        "diet": diet,
        "goal": goal,
        "tried_another_app": triedAnotherApp,
        "hearing_about_us": hearingAboutUs,
        "meals": meals != null ? List<dynamic>.from(meals!.map((x) => x.toJson())) : [],
      };
}

class Meal {
  String type;
  String? time;

  Meal({
    required this.type,
    required this.time,
  });

  factory Meal.fromJson(Map<String, dynamic> json) => Meal(
        type: json["type"],
        time: json["time"],
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "time": time,
      };
}
