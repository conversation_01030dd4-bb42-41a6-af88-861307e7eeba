// import 'dart:async';
// import 'dart:io';

// import 'package:cal/common/extentions/colors_extension.dart';
// import 'package:cal/core/local_models/food_model/food_model.dart';
// import 'package:cal/common/widgets/app_image.dart';
// import 'package:cal/common/widgets/app_text.dart';
// import 'package:cal/generated/assets.dart';
// import 'package:cal/generated/locale_keys.g.dart';
// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_slidable/flutter_slidable.dart';

// class FoodCard extends StatefulWidget {
//   const FoodCard({
//     super.key,
//     required this.foodModel,
//     required this.onDelete,
//   });

//   final FoodModel foodModel;
//   final VoidCallback onDelete;

//   @override
//   State<FoodCard> createState() => _FoodCardState();
// }

// class _FoodCardState extends State<FoodCard> with SingleTickerProviderStateMixin {
//   int _progress = 0;
//   Timer? _successTimer;
//   late AnimationController _controller;
//   late Animation<int> _animatedProgress;

//   @override
//   void initState() {
//     super.initState();
//     _controller = AnimationController(
//       vsync: this,
//       duration: const Duration(seconds: 5),
//     );

//     _animatedProgress = IntTween(begin: 0, end: 90).animate(
//       CurvedAnimation(parent: _controller, curve: Curves.easeInOutCubic),
//     )..addListener(() {
//         setState(() {
//           _progress = _animatedProgress.value;
//         });
//       });

//     if (widget.foodModel.isLoading) {
//       _controller.forward();
//     }
//   }

//   @override
//   void didUpdateWidget(covariant FoodCard oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     if (oldWidget.foodModel.isLoading && !widget.foodModel.isLoading) {
//       _controller.stop();
//       _animateTo100();
//     }
//   }

//   void _animateTo100() {
//     int target = 100;
//     _successTimer = Timer.periodic(const Duration(milliseconds: 30), (timer) {
//       if (_progress < target) {
//         setState(() => _progress++);
//       } else {
//         timer.cancel();
//       }
//     });
//   }

//   @override
//   void dispose() {
//     _successTimer?.cancel();
//     _controller.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final isLoading = widget.foodModel.isLoading;

//     return Slidable(
//       key: ValueKey(widget.foodModel.imagePath),
//       endActionPane: ActionPane(
//         motion: const DrawerMotion(),
//         extentRatio: 0.25,
//         children: [
//           SlidableAction(
//             onPressed: (_) => widget.onDelete(),
//             backgroundColor: Colors.red,
//             foregroundColor: Colors.white,
//             icon: Icons.delete,
//             label: LocaleKeys.home_delete.tr(),
//             borderRadius: BorderRadius.circular(20),
//           ),
//         ],
//       ),
//       child: Container(
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(20),
//           color: const Color(0xfffefefe),
//           boxShadow: [
//             BoxShadow(
//               color: context.onSecondary.withAlpha(35),
//               offset: const Offset(-2, 3),
//               blurRadius: 10,
//             ),
//           ],
//         ),
//         child: Row(
//           children: [
//             ClipRRect(
//               borderRadius: BorderRadius.circular(20),
//               child: Stack(
//                 alignment: Alignment.center,
//                 children: [
//                   Opacity(
//                     opacity: (_progress < 100 && widget.foodModel.isLoading == true) ? 0.6 : 1.0,
//                     child: (widget.foodModel.imagePath != null && File(widget.foodModel.imagePath!).existsSync())
//                         ? Image.file(
//                             File(widget.foodModel.imagePath!),
//                             width: 117,
//                             height: 107,
//                             fit: BoxFit.cover,
//                             errorBuilder: (context, error, stack) => const AppImage.asset(Assets.images3in1, width: 117, height: 107),
//                           )
//                         : const AppImage.asset(
//                             Assets.images3in1,
//                             width: 117,
//                             height: 107,
//                           ),
//                   ),
//                   if (_progress < 100 && widget.foodModel.isLoading == true)
//                     Positioned(
//                       child: SizedBox(
//                         width: 85,
//                         height: 85,
//                         child: Stack(
//                           alignment: Alignment.center,
//                           children: [
//                             CircularProgressIndicator(
//                               value: _progress / 100,
//                               strokeWidth: 6,
//                               backgroundColor: context.tertiary,
//                               valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
//                               constraints: const BoxConstraints(minHeight: 85, minWidth: 85),
//                             ),
//                             Text(
//                               '$_progress%',
//                               style: const TextStyle(
//                                 color: Colors.white,
//                                 fontSize: 13,
//                                 fontWeight: FontWeight.bold,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                 ],
//               ),
//             ),
//             const SizedBox(width: 5),
//             Expanded(
//               child: Padding(
//                 padding: const EdgeInsetsDirectional.symmetric(horizontal: 10),
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.start,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.start,
//                       children: [
//                         isLoading
//                             ? const SizedBox()
//                             : AppText.labelLarge(
//                                 widget.foodModel.dish ?? 'Unknown',
//                                 color: context.onSecondary,
//                                 fontWeight: FontWeight.w500,
//                                 maxLines: 1,
//                               ),
//                         const Spacer(),
//                         Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.circular(10),
//                             color: context.onPrimaryColor,
//                           ),
//                           padding: const EdgeInsets.all(5),
//                           child: isLoading
//                               ? const SizedBox()
//                               : AppText.labelMedium(
//                                   DateFormat('h:mm a').format(widget.foodModel.date!),
//                                   color: context.onSecondary,
//                                   fontWeight: FontWeight.w500,
//                                 ),
//                         ),
//                       ],
//                     ),
//                     const SizedBox(height: 3),
//                     Row(
//                       children: [
//                         const AppImage.asset(Assets.imagesCals),
//                         const SizedBox(width: 5),
//                         isLoading
//                             ? const SizedBox()
//                             : AppText.bodyMedium(
//                                 widget.foodModel.calories?.toString() ?? 'Unknown',
//                                 color: context.onSecondary,
//                                 fontWeight: FontWeight.bold,
//                               ),
//                         const SizedBox(width: 5),
//                         if (!isLoading)
//                           AppText.bodyMedium(
//                             LocaleKeys.home_calorie.tr(),
//                             color: context.onSecondary,
//                             fontWeight: FontWeight.bold,
//                           ),
//                       ],
//                     ),
//                     const SizedBox(height: 4),
//                     if (!isLoading)
//                       Row(
//                         children: [
//                           if (widget.foodModel.protein != null) ...[
//                             const AppImage.asset(Assets.imagesProtien),
//                             const SizedBox(width: 5),
//                             AppText.labelMedium(
//                               '${widget.foodModel.protein} ${LocaleKeys.home_gram.tr()}',
//                               color: context.onSecondary,
//                               fontWeight: FontWeight.bold,
//                             ),
//                             const SizedBox(width: 20),
//                           ],
//                           if (widget.foodModel.carbs != null) ...[
//                             const AppImage.asset(Assets.imagesCarbs),
//                             const SizedBox(width: 5),
//                             AppText.labelMedium(
//                               '${widget.foodModel.carbs} ${LocaleKeys.home_gram.tr()}',
//                               color: context.onSecondary,
//                               fontWeight: FontWeight.bold,
//                             ),
//                             const SizedBox(width: 20),
//                           ],
//                           if (widget.foodModel.fat != null) ...[
//                             const AppImage.asset(Assets.imagesFats),
//                             const SizedBox(width: 5),
//                             AppText.labelMedium(
//                               '${widget.foodModel.fat} ${LocaleKeys.home_gram.tr()}',
//                               color: context.onSecondary,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ],
//                         ],
//                       ),
//                   ],
//                 ),
//               ),
//             )
//           ],
//         ),
//       ),
//     );
//   }
// }
