import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/core/datasources/user_local_data_source.dart';
import 'package:cal/core/local_models/daily_data_model/daily_data_model.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'package:throttling/throttling.dart';

import '../../../../common/widgets/app_text.dart';
import '../../../../common/widgets/large_button.dart';
import '../../../../core/di/injection.dart';
import '../../../../core/local_models/daily_data_model/daily_user_info_service.dart';
import '../../../../core/local_models/user_model/user_model.dart';
import '../../../../generated/locale_keys.g.dart';

class ChangeHeightWeightScreen extends StatefulWidget {
  const ChangeHeightWeightScreen({super.key, required this.height, required this.weight, required this.dailyUserDataModel, required this.user});

  final double height;
  final double weight;
  final DailyUserDataModel dailyUserDataModel;
  final UserModel user;

  @override
  State<ChangeHeightWeightScreen> createState() => _ChangeHeightWeightScreenState();
}

class _ChangeHeightWeightScreenState extends State<ChangeHeightWeightScreen> {
  int selectedHeight = 0;
  int selectedWeight = 0;

  Throttling throttler = Throttling(duration: const Duration(milliseconds: 300));

  @override
  void initState() {
    super.initState();
    selectedHeight = widget.height.toInt();
    selectedWeight = widget.weight.toInt();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsetsDirectional.symmetric(horizontal: 20),
          child: Column(
            children: [
              const SizedBox(height: 30),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    borderRadius: BorderRadius.circular(50),
                    onTap: () {
                      context.pop(false);
                    },
                    child: CircleAvatar(
                      radius: 25,
                      backgroundColor: context.onSecondary.withAlpha(21),
                      child: Icon(
                        Icons.arrow_back,
                        color: context.onSecondary,
                        size: 18,
                      ),
                    ),
                  ),
                  AppText.titleSmall('Edit weight', color: context.onSecondary, fontWeight: FontWeight.w700),
                  const CircleAvatar(
                    radius: 25,
                    backgroundColor: Colors.transparent,
                    child: Icon(
                      Icons.arrow_back,
                      color: Colors.transparent,
                      size: 18,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 100),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      LocaleKeys.onboarding_height.tr(),
                      style: context.textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w600),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      LocaleKeys.onboarding_weight.tr(),
                      style: context.textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w600),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 45),
              Row(
                children: [
                  Expanded(
                    child: heightWeightSelector(
                      value: selectedHeight,
                      onSelected: (index) {
                        // HapticFeedbackUtil.vibrate();
                        throttler.throttle(() {
                          setState(() {
                            selectedHeight = index + 70;
                          });
                        });
                      },
                      isHeight: true,
                      controller: FixedExtentScrollController(initialItem: selectedHeight),
                    ),
                  ),
                  Expanded(
                    child: heightWeightSelector(
                      value: selectedWeight,
                      onSelected: (index) {
                        // HapticFeedbackUtil.vibrate();
                        throttler.throttle(() {
                          setState(() {
                            selectedWeight = index + 30;
                          });
                        });
                      },
                      isHeight: false,
                      controller: FixedExtentScrollController(initialItem: selectedWeight),
                    ),
                  ),
                ],
              ),
              const Spacer(),
              LargeButton(
                onPressed: () async {
                  await UserLocalDataSource(getIt<Isar>()).saveUserData(widget.user.copyWith(height: selectedHeight.toString())).then((val) async {
                    await DailyUserInfoService.saveDailyData(widget.dailyUserDataModel.copyWith(weight: selectedWeight.toDouble())).then((val) {
                      if (context.mounted) {
                        context.pop(true);
                      }
                    });
                  });
                },
                text: 'Save Changes',
                backgroundColor: context.primaryColor,
                circularRadius: 16,
                textStyle: context.textTheme.bodyMedium!.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget heightWeightSelector({
    Function(int)? onSelected,
    FixedExtentScrollController? controller,
    required bool isHeight,
    required int value,
  }) =>
      SizedBox(
        height: 200,
        child: ListWheelScrollView.useDelegate(
          itemExtent: 40,
          diameterRatio: 1.5,
          physics: const FixedExtentScrollPhysics(),
          onSelectedItemChanged: onSelected,
          controller: controller,
          childDelegate: ListWheelChildBuilderDelegate(
            childCount: isHeight ? 271 : 371,
            builder: (context, index) {
              final height = index + (isHeight ? 70 : 30);
              final isSelected = height == value;
              return Center(
                child: Text(
                  '$height ${isHeight ? 'CM' : 'KG'}',
                  style: context.textTheme.bodyMedium!.copyWith(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? context.primaryColor : Colors.black,
                  ),
                ),
              );
            },
          ),
        ),
      );
}
