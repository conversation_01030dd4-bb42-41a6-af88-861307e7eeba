# Authentication Feature

This feature provides Login with Apple and Login with Google functionality integrated with a custom backend.

## Architecture

The authentication feature follows Clean Architecture principles with three layers:

- **Presentation Layer**: UI components and BLoC for state management
- **Domain Layer**: Business logic, entities, and use cases
- **Data Layer**: Data sources and repository implementations

## Dependencies

Add these dependencies to your `pubspec.yaml`:

```yaml
dependencies:
  sign_in_with_apple: ^6.1.2
  google_sign_in: ^6.2.1
  flutter_secure_storage: ^9.2.2
  dio: ^5.7.0
  flutter_bloc: ^8.1.6
  get_it: ^8.0.2
```

## Setup

### 1. Initialize Dependencies

In your `main.dart`, initialize the authentication dependencies:

```dart
import 'features/authentication/di/authentication_injection.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize authentication dependencies
  AuthenticationInjection.init();
  
  runApp(MyApp());
}
```

### 2. Configure Backend URL

Update the backend URL in `authentication_injection.dart`:

```dart
sl.registerLazySingleton<AuthenticationRemoteDataSource>(
  () => AuthenticationRemoteDataSourceImpl(
    dio: sl(),
    baseUrl: 'https://your-actual-backend-url.com', // Update this
    googleSignIn: sl(),
  ),
);
```

### 3. Platform-specific Setup

#### iOS Setup for Apple Sign In

1. Enable "Sign In with Apple" capability in Xcode
2. Add your Team ID and Bundle ID to Apple Developer Console
3. Create a Service ID for your app

#### Android Setup for Google Sign In

1. Add your SHA-1 fingerprint to Firebase Console
2. Download and add `google-services.json` to `android/app/`
3. Configure OAuth consent screen in Google Cloud Console

### 4. Usage

Wrap your app with the BLoC provider:

```dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'features/authentication/di/authentication_injection.dart';
import 'features/authentication/presentation/bloc/authentication_bloc.dart';
import 'features/authentication/presentation/pages/authentication_page.dart';

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: BlocProvider(
        create: (context) => sl<AuthenticationBloc>()
          ..add(const AuthenticationStarted()),
        child: const AuthenticationPage(),
      ),
    );
  }
}
```

## Backend Integration

Your backend should implement these endpoints:

### POST /auth/apple
Receives Apple authorization code and returns your backend token.

**Request:**
```json
{
  "authorization_code": "string",
  "identity_token": "string",
  "user_identifier": "string",
  "email": "string",
  "given_name": "string",
  "family_name": "string"
}
```

**Response:**
```json
{
  "access_token": "your_backend_jwt_token",
  "refresh_token": "optional_refresh_token",
  "expires_at": "2024-12-31T23:59:59Z"
}
```

### POST /auth/google
Receives Google ID token and returns your backend token.

**Request:**
```json
{
  "id_token": "string",
  "access_token": "string",
  "email": "string",
  "display_name": "string",
  "photo_url": "string"
}
```

**Response:**
```json
{
  "access_token": "your_backend_jwt_token",
  "refresh_token": "optional_refresh_token",
  "expires_at": "2024-12-31T23:59:59Z"
}
```

## Security Considerations

1. Always verify tokens on your backend
2. Use HTTPS for all communications
3. Store tokens securely using `flutter_secure_storage`
4. Implement token refresh logic
5. Handle token expiration gracefully

## Error Handling

The implementation includes comprehensive error handling for:
- Network failures
- Authentication cancellation
- Token verification failures
- Backend communication errors

Errors are displayed to users via SnackBar messages and state management.

