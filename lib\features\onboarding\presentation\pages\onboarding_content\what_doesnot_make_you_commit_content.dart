import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_option.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Goal Selection Screen
class WhatDoesnotMakeYouCommitContent extends StatefulWidget {
  const WhatDoesnotMakeYouCommitContent({super.key});

  @override
  State<WhatDoesnotMakeYouCommitContent> createState() => _GoalSelectionContentState();
}

class _GoalSelectionContentState extends State<WhatDoesnotMakeYouCommitContent> {
  @override
  void initState() {
    super.initState();
    context.read<OnboardingBloc>().add(RegisterScreenValidation((state) => state.whatMakesYouDoesnotCommit != null));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_what_doesnot_make_you_commit.tr(),
          centerContent: true,
          contentWidgets: [
            ...WhatMakesYouDoesnotCommit.values
                .map(
                  (whatMakesYouDoesnotCommit) => OnboardingOption(
                    imagePath: whatMakesYouDoesnotCommit.icon,
                    isSelected: state.whatMakesYouDoesnotCommit == whatMakesYouDoesnotCommit,
                    text: whatMakesYouDoesnotCommit.localizedName,
                    onSelected: () =>
                        context.read<OnboardingBloc>().add(UpdateWhatMakesYouDoesnotCommit(whatMakesYouDoesnotCommit)),
                  ),
                )
                .toList(),
          ],
        );
      },
    );
  }
}
