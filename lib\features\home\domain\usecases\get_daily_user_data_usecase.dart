import 'package:cal/core/local_models/daily_data_model/daily_data_model.dart';
import 'package:cal/features/home/<USER>/repositories/home_repository.dart';
import 'package:injectable/injectable.dart';

@injectable
class GetDailyUserDataUseCase {
  final HomeRepository repository;

  GetDailyUserDataUseCase({required this.repository});

  Future<DailyUserDataModel> call(DateTime date) {
    return repository.getDailyUserData(date);
  }
}
