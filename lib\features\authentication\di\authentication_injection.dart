import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:get_it/get_it.dart';

// Data Sources
import '../data/datasources/authentication_local_datasource.dart';
import '../data/datasources/authentication_remote_datasource.dart';

// Repositories
import '../data/repositories/authentication_repository_impl.dart';
import '../domain/repositories/authentication_repository.dart';

// Use Cases
import '../domain/usecases/sign_in_with_apple_usecase.dart';
import '../domain/usecases/sign_in_with_google_usecase.dart';
import '../domain/usecases/sign_out_usecase.dart';
import '../domain/usecases/get_current_user_usecase.dart';

// BLoC
import '../presentation/bloc/authentication_bloc.dart';

final GetIt sl = GetIt.instance;

class AuthenticationInjection {
  static void init() {
    // External dependencies
    sl.registerLazySingleton<FlutterSecureStorage>(
      () => const FlutterSecureStorage(),
    );

    sl.registerLazySingleton<Dio>(() {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);
      return dio;
    });

    sl.registerLazySingleton<GoogleSignIn>(() {
      return GoogleSignIn(
        scopes: [
          'email',
          'profile',
        ],
      );
    });

    // Data sources
    sl.registerLazySingleton<AuthenticationLocalDataSource>(
      () => AuthenticationLocalDataSourceImpl(sl()),
    );

    sl.registerLazySingleton<AuthenticationRemoteDataSource>(
      () => AuthenticationRemoteDataSourceImpl(
        firebaseAuth: FirebaseAuth.instance,
        googleSignIn: sl(),
        httpClient: sl(),
      ),
    );

    // Repository
    sl.registerLazySingleton<AuthenticationRepository>(
      () => AuthenticationRepositoryImpl(
        remoteDataSource: sl(),
        localDataSource: sl(),
      ),
    );

    // Use cases
    sl.registerLazySingleton(() => SignInWithAppleUseCase(sl()));
    sl.registerLazySingleton(() => SignInWithGoogleUseCase(sl()));
    sl.registerLazySingleton(() => SignOutUseCase(sl()));
    sl.registerLazySingleton(() => GetCurrentUserUseCase(sl()));

    // BLoC
    sl.registerFactory(
      () => AuthenticationBloc(
        signInWithAppleUseCase: sl(),
        signInWithGoogleUseCase: sl(),
        signOutUseCase: sl(),
        getCurrentUserUseCase: sl(),
      ),
    );
  }
}
