import 'package:cal/core/network/exceptions.dart';
import 'package:cal/core/local_models/exercise_model/exercise_model.dart';
import 'package:cal/features/quick_actions/exercise/data/datasources/exercise_remote_datasource.dart';
import 'package:cal/features/quick_actions/exercise/data/datasources/exercise_local_datasource.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_ai_model.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_model.dart';
import 'package:cal/features/quick_actions/exercise/domain/repositories/exercise_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

@Injectable(as: ExerciseRepository)
class ExerciseRepositoryImpl implements ExerciseRepository {
  final ExerciseRemoteDataSource remoteDataSource;
  final ExerciseLocalDataSource localDataSource;

  ExerciseRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<Either<String, ExerciseModel>> saveExercise(ExerciseSaveModel exercise) async {
    try {
      final Either<Failure, ExerciseModel> result = await remoteDataSource.saveExercise(exercise);
      return await result.fold(
        (failure) async => Left(failure.toString()),
        (model) async {
          // Save to local database after successful API call
          try {
            await localDataSource.saveExercise(model);
            return Right(model);
          } catch (e) {
            // Even if local save fails, return the API result
            return Right(model);
          }
        },
      );
    } catch (e) {
      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, ExerciseModel>> saveExerciseAi(ExerciseSaveAiModel exercise) async {
    try {
      final Either<Failure, ExerciseModel> result = await remoteDataSource.saveExerciseAi(exercise);
      return await result.fold(
        (failure) async => Left(failure.toString()),
        (model) async {
          // Save to local database after successful API call
          try {
            await localDataSource.saveExercise(model);
            return Right(model);
          } catch (e) {
            // Even if local save fails, return the API result
            return Right(model);
          }
        },
      );
    } catch (e) {
      return Left(e.toString());
    }
  }
}
