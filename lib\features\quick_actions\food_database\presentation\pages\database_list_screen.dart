import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/common/widgets/custom_text_field.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/food_database_card.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:throttling/throttling.dart';

import '../../domain/usecases/search_meals_use_case.dart';

class DatabaseListScreen extends StatefulWidget {
  const DatabaseListScreen({super.key});

  @override
  State<DatabaseListScreen> createState() => _CreateMealScreenState();
}

class _CreateMealScreenState extends State<DatabaseListScreen> {
  final TextEditingController mealController = TextEditingController();
  // ignore: unused_field
  String _searchQuery = '';
  String? mealName;
  String mealCals = "0";
  String mealProtien = "0";
  String mealCarb = "0";
  String mealFat = "0";

  @override
  void initState() {
    super.initState();
    context.read<FoodDatabaseBloc>().add(const LoadDatabaseFoodEvent());
    context.read<FoodDatabaseBloc>().add(SearchFoodEvent(params: SearchMealsParams(query: '')));
    mealController.addListener(() {
      setState(() {
        _searchQuery = mealController.text.trim().toLowerCase();
      });
    });
  }

  final deb = Debouncing(duration: const Duration(milliseconds: 500));

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: context.background,
        appBar: AppBar(
          forceMaterialTransparency: true,
          elevation: 0,
          centerTitle: true,
          leading: Padding(
            padding: const EdgeInsets.only(right: 18.0),
            child: GestureDetector(
                onTap: () {
                  context.pop();
                },
                child: const Icon(Icons.arrow_back)),
          ),
          automaticallyImplyLeading: false,
          titleSpacing: 0,
          title: Text(
            LocaleKeys.food_database_create_meal.tr(),
            style: context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w500, color: context.primaryColor),
            textAlign: TextAlign.start,
          ),
        ),
        body: BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
          builder: (context, state) {
            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsetsDirectional.only(end: 26, start: 26, top: 2, bottom: 50),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomTextField(
                      controller: mealController,
                      hint: LocaleKeys.food_database_describe_what_you_ate.tr(),
                      textInputAction: TextInputAction.search,
                      keyboardType: TextInputType.text,
                      onChanged: (value) {
                        deb.debounce(() {
                          context.read<FoodDatabaseBloc>().add(SearchFoodEvent(params: SearchMealsParams(query: value)));
                        });
                      },
                      onFieldSubmitted: (value) {
                        context.read<FoodDatabaseBloc>().add(SearchFoodEvent(params: SearchMealsParams(query: value)));
                      },
                    ),
                    const SizedBox(height: 20),
                    Align(
                      alignment: Alignment.centerRight,
                      child: AppText.titleLarge(
                        LocaleKeys.food_database_food_database.tr(),
                        color: context.onSecondary,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    const SizedBox(height: 19),
                    BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
                      builder: (context, state) {
                        switch (state.searchedFoodStatus) {
                          case null:
                            return const SizedBox.shrink();
                          case BlocStatus.initial:
                            return const Center(
                              child: CircularProgressIndicator.adaptive(),
                            );
                          case BlocStatus.loading:
                            return const Center(
                              child: CircularProgressIndicator.adaptive(),
                            );
                          case BlocStatus.success:
                            return state.searchedFood!.isNotEmpty
                                ? ListView.separated(
                                    shrinkWrap: true,
                                    physics: const NeverScrollableScrollPhysics(),
                                    itemBuilder: (context, i) => FoodDatabaseCard(
                                      title: state.searchedFood?[i].foodName ?? "unknown",
                                      cals: state.searchedFood?[i].foodDescription.calories.toString(),
                                      onAddTap: () {
                                        context.read<FoodDatabaseBloc>().add(
                                              AddIngredientToMyMealEvent(
                                                DatabaseFoodModel(
                                                  dish: state.searchedFood![i].foodName,
                                                  calories: state.searchedFood![i].foodDescription.calories.toInt(),
                                                  fat: state.searchedFood![i].foodDescription.fat.toDouble(),
                                                  protein: state.searchedFood![i].foodDescription.protein.toDouble(),
                                                  carbs: state.searchedFood![i].foodDescription.carbs.toDouble(),
                                                ),
                                              ),
                                            );
                                        context.pop();
                                      },
                                    ),
                                    separatorBuilder: (context, index) => const SizedBox(height: 16),
                                    itemCount: state.searchedFood!.length,
                                  )
                                : Align(
                                    alignment: Alignment.center,
                                    child: AppText.bodyMedium(
                                      LocaleKeys.food_database_no_food.tr(),
                                      color: context.onSecondary,
                                      fontWeight: FontWeight.w300,
                                    ),
                                  );
                          case BlocStatus.error:
                            return Align(
                              alignment: Alignment.center,
                              child: AppText.bodyMedium(
                                LocaleKeys.food_database_no_food.tr(),
                                color: context.onSecondary,
                                fontWeight: FontWeight.w300,
                              ),
                            );
                        }
                      },
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
