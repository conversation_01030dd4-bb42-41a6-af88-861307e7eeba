import 'package:equatable/equatable.dart';
import 'package:in_app_purchase/in_app_purchase.dart' as iap;
import 'package:cal/features/subscriptions/enums.dart';

class PurchaseDetailsWrapper extends Equatable {
  final String productId;
  final String transactionId;
  final DateTime purchaseDate;
  final SubscriptionPurchaseStatus status;
  final String? error;

  const PurchaseDetailsWrapper({
    required this.productId,
    required this.transactionId,
    required this.purchaseDate,
    required this.status,
    this.error,
  });

  factory PurchaseDetailsWrapper.fromPurchaseDetails(iap.PurchaseDetails details) {
    return PurchaseDetailsWrapper(
      productId: details.productID,
      transactionId: details.purchaseID ?? '',
      purchaseDate: DateTime.now(), 
      status: _mapStatus(details.status),
      error: details.error?.message,
    );
  }

  static SubscriptionPurchaseStatus _mapStatus(iap.PurchaseStatus? status) {
    switch (status) {
      case iap.PurchaseStatus.pending:
        return SubscriptionPurchaseStatus.loading;
      case iap.PurchaseStatus.purchased:
        return SubscriptionPurchaseStatus.purchased;
      case iap.PurchaseStatus.error:
        return SubscriptionPurchaseStatus.error;
      default:
        return SubscriptionPurchaseStatus.initial;
    }
  }

  @override
  List<Object?> get props => [productId, transactionId, purchaseDate, status, error];

  PurchaseDetailsWrapper copyWith({
    String? productId,
    String? transactionId,
    DateTime? purchaseDate,
    SubscriptionPurchaseStatus? status,
    String? error,
  }) {
    return PurchaseDetailsWrapper(
      productId: productId ?? this.productId,
      transactionId: transactionId ?? this.transactionId,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      status: status ?? this.status,
      error: error ?? this.error,
    );
  }
}
