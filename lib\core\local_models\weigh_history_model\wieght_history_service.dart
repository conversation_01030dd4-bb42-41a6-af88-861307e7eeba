import 'package:cal/core/local_models/weigh_history_model/weight_history_model.dart';
import 'package:isar/isar.dart';

import '../../di/injection.dart';

class WeightHistoryService {
  static Future<void> saveWeightHistory(WeightHistoryModel model) async {
    final isar = getIt<Isar>();
    await isar.writeTxn(() async {
      await isar.weightHistoryModels.put(model);
    });
  }

  static Future<List<WeightHistoryModel>> getWeightHistory() async {
    final isar = getIt<Isar>();
    return await isar.weightHistoryModels.where().findAll();
  }
}
