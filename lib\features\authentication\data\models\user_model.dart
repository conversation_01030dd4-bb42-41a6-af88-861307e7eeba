import '../../domain/entities/user_entity.dart';

// Data Model for User
class UserModel extends UserEntity {
  const UserModel({
    required super.id,
    required super.email,
    super.name,
    required super.authProvider,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String?,
      authProvider: AuthProvider.values.firstWhere(
        (e) => e.name == json['auth_provider'],
        orElse: () => AuthProvider.google,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'auth_provider': authProvider.name,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    AuthProvider? authProvider,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      authProvider: authProvider ?? this.authProvider,
    );
  }
}

