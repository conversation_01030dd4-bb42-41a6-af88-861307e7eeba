import 'package:flutter/widgets.dart';

import '../../domain/entities/user_entity.dart';

// Authentication Events
abstract class AuthenticationEvent {
  const AuthenticationEvent();
}

class AuthenticationStarted extends AuthenticationEvent {
  const AuthenticationStarted();
}

class SignInWithApplePressed extends AuthenticationEvent {
  final BuildContext context;
  const SignInWithApplePressed({required this.context});
}

class SignInWithGooglePressed extends AuthenticationEvent {
  const SignInWithGooglePressed();
}

class SignOutPressed extends AuthenticationEvent {
  const SignOutPressed();
}

class AuthenticationStatusChanged extends AuthenticationEvent {
  final UserEntity? user;

  const AuthenticationStatusChanged(this.user);
}
