import 'package:cal/core/local_models/user_model/user_model.dart';
import 'package:cal/features/onboarding/domain/repositories/onboarding_repository.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class LocalDeleteClearUserUseCase {

  final OnboardingRepository onboardingRepository;
  LocalDeleteClearUserUseCase({required this.onboardingRepository});

  Future<void> deleteClearUser(UserModel user, bool isDelete){
    return isDelete ? onboardingRepository.deleteUserData(user) : onboardingRepository.clearUserData();
  }

}