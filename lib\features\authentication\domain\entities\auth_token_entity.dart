// Domain Entity for Authentication Token
class AuthTokenEntity {
  final String accessToken;
  final String? refreshToken;
  final DateTime expiresAt;

  const AuthTokenEntity({
    required this.accessToken,
    this.refreshToken,
    required this.expiresAt,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthTokenEntity &&
        other.accessToken == accessToken &&
        other.refreshToken == refreshToken &&
        other.expiresAt == expiresAt;
  }

  @override
  int get hashCode {
    return accessToken.hashCode ^
        refreshToken.hashCode ^
        expiresAt.hashCode;
  }

  @override
  String toString() {
    return 'AuthTokenEntity(accessToken: $accessToken, refreshToken: $refreshToken, expiresAt: $expiresAt)';
  }
}

