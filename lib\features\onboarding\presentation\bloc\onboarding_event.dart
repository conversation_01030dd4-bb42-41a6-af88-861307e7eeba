part of 'onboarding_bloc.dart';

@immutable
abstract class OnboardingEvent extends Equatable {
  const OnboardingEvent();

  @override
  List<Object?> get props => [];
}

class InitializeBloc extends OnboardingEvent {
  final bool isFromSettings;

  const InitializeBloc({this.isFromSettings = false});
}

class NextStep extends OnboardingEvent {
  final BuildContext context;
  const NextStep(this.context);
}

class UpdateAddCalsAgainToTarget extends OnboardingEvent {
  final bool value;
  const UpdateAddCalsAgainToTarget(this.value);
}

class UpdateAddYesterdayCals extends OnboardingEvent {
  final bool value;
  const UpdateAddYesterdayCals(this.value);
}

class PreviousStep extends OnboardingEvent {}

class SubmitOnboarding extends OnboardingEvent {
  const SubmitOnboarding();

  @override
  List<Object?> get props => [];
}

class UpdateGender extends OnboardingEvent {
  final Gender gender;
  const UpdateGender(this.gender);

  @override
  List<Object?> get props => [gender];
}

class UpdateExerciseFrequency extends OnboardingEvent {
  final ExerciseFrequency frequency;
  const UpdateExerciseFrequency(this.frequency);

  @override
  List<Object?> get props => [frequency];
}

class UpdateWhereDidYouHeardAboutUs extends OnboardingEvent {
  final HeardAboutUs heardAboutUs;
  const UpdateWhereDidYouHeardAboutUs(this.heardAboutUs);

  @override
  List<Object?> get props => [heardAboutUs];
}

class UpdateHasUsedOtherApps extends OnboardingEvent {
  final Choice choice;
  const UpdateHasUsedOtherApps(this.choice);

  @override
  List<Object?> get props => [choice];
}

class UpdateHeight extends OnboardingEvent {
  final int height;
  const UpdateHeight(this.height);

  @override
  List<Object?> get props => [height];
}

class UpdateWeight extends OnboardingEvent {
  final int weight;
  const UpdateWeight(this.weight);

  @override
  List<Object?> get props => [weight];
}

class UpdateTargetWeight extends OnboardingEvent {
  final double? targetWeight;
  const UpdateTargetWeight(this.targetWeight);

  @override
  List<Object?> get props => [targetWeight];
}

class UpdateBirthDay extends OnboardingEvent {
  final int day;

  const UpdateBirthDay(this.day);

  @override
  List<Object?> get props => [day];
}

class UpdateBirthMonth extends OnboardingEvent {
  final int month;

  const UpdateBirthMonth(this.month);

  @override
  List<Object?> get props => [month];
}

class UpdateBirthYear extends OnboardingEvent {
  final int year;

  const UpdateBirthYear(this.year);

  @override
  List<Object?> get props => [year];
}

class UpdateGoal extends OnboardingEvent {
  final Goal goal;
  final bool isFromSettings;

  const UpdateGoal(this.goal, this.isFromSettings);

  @override
  List<Object?> get props => [goal];
}

class UpdateWhatMakesYouDoesnotCommit extends OnboardingEvent {
  final WhatMakesYouDoesnotCommit whatMakesYouDoesnotCommit;
  const UpdateWhatMakesYouDoesnotCommit(this.whatMakesYouDoesnotCommit);

  @override
  List<Object?> get props => [whatMakesYouDoesnotCommit];
}

class UpdateWhatIsYourDiet extends OnboardingEvent {
  final Diet diet;
  const UpdateWhatIsYourDiet(this.diet);

  @override
  List<Object?> get props => [diet];
}

class UpdateWhatYouWantToAchieve extends OnboardingEvent {
  final Achieve achieve;
  const UpdateWhatYouWantToAchieve(this.achieve);

  @override
  List<Object?> get props => [achieve];
}

class RegisterScreenValidation extends OnboardingEvent {
  final bool Function(OnboardingState) validationFunction;

  const RegisterScreenValidation(this.validationFunction);

  @override
  List<Object?> get props => [];
}

class UpdateWeightChangeRate extends OnboardingEvent {
  final double rate;
  const UpdateWeightChangeRate(this.rate);

  @override
  List<Object?> get props => [rate];
}

class UpdateMealtime extends OnboardingEvent {
  final String? firstMeal;
  final String? secondMeal;
  final String? thirdMeal;
  const UpdateMealtime({this.firstMeal, this.secondMeal, this.thirdMeal});

  @override
  List<Object?> get props => [firstMeal, secondMeal, thirdMeal];
}

// New event for rebuilding the flow when needed
class RebuildFlow extends OnboardingEvent {}

class UpdateNutritionValues extends OnboardingEvent {
  final int? calories;
  final int? carbs;
  final int? protein;
  final int? fat;

  const UpdateNutritionValues({
    this.calories,
    this.carbs,
    this.protein,
    this.fat,
  });

  @override
  List<Object?> get props => [calories, carbs, protein, fat];
}

class ProcessingComplete extends OnboardingEvent {
  const ProcessingComplete();

  @override
  List<Object?> get props => [];
}
