import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/common/widgets/large_button.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/widgets/nutritions_textfield.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_metric_card.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class OnboardingManualNutritionEditScreen extends StatefulWidget {
  final String nutritionValue;
  final CardType cardType;
  final BuildContext context;
  const OnboardingManualNutritionEditScreen(
      {super.key, required this.nutritionValue, required this.cardType, required this.context});

  @override
  State<OnboardingManualNutritionEditScreen> createState() => _ManualNutritionEditScreenState();
}

class _ManualNutritionEditScreenState extends State<OnboardingManualNutritionEditScreen> {
  late final TextEditingController _nutritionsController;

  // Do not create or close the bloc here. It should be provided higher up in the widget tree.
  late OnboardingBloc _onboardingBloc;

  @override
  void initState() {
    _nutritionsController = TextEditingController(text: widget.nutritionValue);
    // Access the existing bloc from the context. Do not create a new one.
    _onboardingBloc = widget.context.read<OnboardingBloc>();
    super.initState();
  }

  @override
  void dispose() {
    _nutritionsController.dispose();
    // Do not close the bloc here, as it\'s a lazySingleton and managed by getIt.
    // If you close it here, it will be closed for all other parts of the app.
    super.dispose();
  }

  String getTitle() {
    switch (widget.cardType) {
      case CardType.cals:
        return LocaleKeys.onboarding_target_calories.tr();
      case CardType.carbs:
        return LocaleKeys.onboarding_target_carbs.tr();
      case CardType.fat:
        return LocaleKeys.onboarding_target_fats.tr();
      case CardType.protien:
        return LocaleKeys.onboarding_target_proteins.tr();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            AppImage.asset(
              Assets.imagesCals,
              color: context.primaryColor,
              size: 25,
            ),
            const SizedBox(width: 10),
            Text(
              LocaleKeys.onboarding_manual_input.tr(),
              style:
                  context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w900, color: context.primaryColor),
            ),
          ],
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 15.0, horizontal: 35),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    TweenAnimationBuilder<double>(
                      duration: const Duration(milliseconds: 800),
                      curve: Curves.easeInOut,
                      tween: Tween<double>(
                        begin: 0,
                        end: 0.5,
                      ),
                      builder: (context, value, _) {
                        return SizedBox(
                          width: context.screenWidth * .18,
                          height: context.screenWidth * .18,
                          child: CircularProgressIndicator(
                            value: value,
                            strokeWidth: 5,
                            backgroundColor: context.onSecondary.withAlpha(15),
                            valueColor: AlwaysStoppedAnimation<Color>(context.primaryColor),
                          ),
                        );
                      },
                    ),

                    // Icon in the center
                    Container(
                      padding: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: context.onSecondary.withAlpha(15),
                        shape: BoxShape.circle,
                      ),
                      child: const AppImage.asset(Assets.imagesCals),
                    ),
                  ],
                ),
                const SizedBox(width: 10),
                Expanded(
                    child: NutritionsTextfield(
                  controller: _nutritionsController,
                  hint: getTitle(),
                )),
              ],
            ),
            LargeButton(
              onPressed: () {
                switch (widget.cardType) {
                  case CardType.cals:
                    _onboardingBloc.add(UpdateNutritionValues(calories: int.parse(_nutritionsController.text)));
                    break;
                  case CardType.carbs:
                    _onboardingBloc.add(UpdateNutritionValues(carbs: int.parse(_nutritionsController.text)));
                    break;
                  case CardType.fat:
                    _onboardingBloc.add(UpdateNutritionValues(fat: int.parse(_nutritionsController.text)));
                    break;
                  case CardType.protien:
                    _onboardingBloc.add(UpdateNutritionValues(protein: int.parse(_nutritionsController.text)));
                    break;
                }

                Navigator.of(context).pop(); // Or however you\'re navigating
              },
              text: LocaleKeys.home_add.tr(),
              circularRadius: 16,
              backgroundColor: context.primaryColor,
              textStyle: context.textTheme.bodyMedium!.copyWith(
                color: context.onPrimaryColor,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
