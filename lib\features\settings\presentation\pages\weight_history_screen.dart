import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/core/local_models/weigh_history_model/weight_history_model.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../../../core/local_models/weigh_history_model/wieght_history_service.dart';
import '../../../../generated/locale_keys.g.dart';
import '../widgets/personal_details_app_bar.dart';

class WeightHistoryScreen extends StatefulWidget {
  const WeightHistoryScreen({super.key});

  @override
  State<WeightHistoryScreen> createState() => _WeightHistoryScreenState();
}

class _WeightHistoryScreenState extends State<WeightHistoryScreen> {
  List<WeightHistoryModel> data = [];

  @override
  void initState() {
    super.initState();
    fetchWeightHistory();
  }

  fetchWeightHistory() async {
    data = await WeightHistoryService.getWeightHistory();
    data = data.reversed.toList();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.background,
      body: SafeArea(
        child: Column(
          children: [
            const SizedBox(height: 40),
            Padding(
              padding: const EdgeInsetsDirectional.symmetric(horizontal: 20),
              child: PersonalDetailsAppBar(title: LocaleKeys.settings_weight_log.tr()),
            ),
            const SizedBox(height: 28),
            Expanded(
              child: ListView.separated(
                scrollDirection: Axis.vertical,
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 20),
                itemBuilder: (context, index) => Container(
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(12), color: context.onPrimaryColor, boxShadow: [
                    BoxShadow(
                      color: context.onSecondary.withAlpha(51),
                      offset: const Offset(-2, 4),
                      blurRadius: 10,
                    ),
                  ]),
                  padding: const EdgeInsetsDirectional.all(12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${data[index].weight} كغ',
                        style: context.textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w900),
                      ),
                      Text(
                        context.locale.languageCode == 'en'
                            ? DateFormat('MMM yyyy ,dd').format(data[index].date)
                            : '${DateFormat('dd, yyyy').format(data[index].date)} ${DateFormat('MMM', context.locale.languageCode).format(data[index].date)}',
                        style: context.textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w900),
                      ),
                    ],
                  ),
                ),
                separatorBuilder: (context, index) => const SizedBox(height: 15),
                itemCount: data.length,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
