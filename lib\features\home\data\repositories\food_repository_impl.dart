import 'package:injectable/injectable.dart';
import 'package:cal/features/home/<USER>/datasources/local_food_data_source.dart';
import 'package:cal/features/home/<USER>/repositories/food_repository.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';

@Injectable(as: FoodRepository)
class FoodRepositoryImpl implements FoodRepository {
  final LocalFoodDataSource localDataSource;

  const FoodRepositoryImpl({required this.localDataSource});

  @override
  Future<void> saveMeal(FoodModel meal) async {
    await localDataSource.saveMeal(meal);
  }

  @override
  Future<List<FoodModel>> getAllFood(DateTime date) async {
    return await localDataSource.getAllMeals(date);
  }

  @override
  Future<void> updateMeal(FoodModel meal) async {
    await localDataSource.updateFood(meal);
  }

  @override
  Future<void> clearFood() async {
    await localDataSource.clearMeals();
  }

  @override
  Future<void> deleteFood(FoodModel food) async {
    await localDataSource.deleteFood(food);
  }
}
