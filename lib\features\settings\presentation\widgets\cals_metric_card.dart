import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class CalsMetricCard extends StatelessWidget {
  const CalsMetricCard({super.key, required this.data});

  final List<MacroData> data;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 200,
      child: SfCircular<PERSON>hart(
        annotations: <CircularChartAnnotation>[
          CircularChartAnnotation(
            widget: CircleAvatar(
              backgroundColor: Colors.grey.withAlpha(127),
              radius: 25,
              child: const Icon(
                Icons.local_fire_department,
                color: Colors.black,
                size: 30,
              ),
            ),
          ),
        ],
        series: <DoughnutSeries<MacroData, String>>[
          DoughnutSeries<MacroData, String>(
            dataSource: data,
            xValueMapper: (MacroData data, _) => data.name,
            yValueMapper: (MacroData data, _) => data.grams,
            pointColorMapper: (MacroData data, _) => data.color,
            radius: '75%',
            innerRadius: '85%',
            dataLabelMapper: (MacroData data, _) => ' ${data.grams.toInt()} غ  ',
            dataLabelSettings: const DataLabelSettings(
              isVisible: true,
              borderRadius: 12,
              useSeriesColor: true,
              labelPosition: ChartDataLabelPosition.outside,
              textStyle: TextStyle(fontSize: 14, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}

class MacroData {
  final String name;
  final double grams;
  final Color color;

  MacroData(this.name, this.grams, this.color);
}
