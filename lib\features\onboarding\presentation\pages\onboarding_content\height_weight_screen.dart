import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/height_weight_selector.dart';

import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Height Weight Screen
class HeightWeightContent extends StatefulWidget {
  const HeightWeightContent({super.key});

  @override
  State<HeightWeightContent> createState() => _HeightWeightContentState();
}

class _HeightWeightContentState extends State<HeightWeightContent> {
  Widget _buildLabels(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            LocaleKeys.onboarding_height.tr(),
            style: context.textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w600),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          child: Text(
            LocaleKeys.onboarding_weight.tr(),
            style: context.textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w600),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildWheels(BuildContext context, OnboardingState state) {
    return Row(
      children: [
        Expanded(
          child: HeightSelector(
            initialHeight: state.height ?? 170,
            onHeightChanged: (height) => context.read<OnboardingBloc>().add(UpdateHeight(height)),
          ),
        ),
        Expanded(
          child: WeightSelector(
            initialWeight: state.weight ?? 70,
            onWeightChanged: (weight) => context.read<OnboardingBloc>().add(UpdateWeight(weight)),
          ),
        ),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    context.read<OnboardingBloc>().add(RegisterScreenValidation((state) => state.height != null && state.weight != null));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_height_and_weight.tr(),
          description: LocaleKeys.onboarding_we_will_use_this_information_to_personalize_your_plan.tr(),
          contentWidgets: [
            const SizedBox(height: 60),
            _buildLabels(context),
            const SizedBox(height: 16),
            _buildWheels(context, state),
          ],
        );
      },
    );
  }
}
