import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class DateHelper {
  static List<List<Map<String, String>>> getPastFourWeeks(Locale locale) {
    DateTime now = DateTime.now();
    int currentWeekday = now.weekday;

    int daysFromSaturday = ((currentWeekday + 1) % 7);
    DateTime startOfThisWeek = now.subtract(Duration(days: daysFromSaturday));

    const List<String> shortEnglishDays = ['Sa', 'Su', 'Mo', 'Tu', 'We', 'Th', 'Fr'];
    const List<String> shortArabicDays = ['س', 'اح', 'اث', 'ث', 'ار', 'خ', 'ج'];

    List<List<Map<String, String>>> allWeeks = [];

    for (int w = 0; w < 4; w++) {
      DateTime weekStart = startOfThisWeek.subtract(Duration(days: 7 * w));
      List<Map<String, String>> week = [];

      for (int i = 0; i < 7; i++) {
        DateTime day = weekStart.add(Duration(days: i));

        String date = DateFormat('dd').format(day);

        DateTime fullDate = day;

        DateTime nowDateOnly = DateTime(now.year, now.month, now.day);
        DateTime dayDateOnly = DateTime(day.year, day.month, day.day);

        String status;
        if (dayDateOnly.isBefore(nowDateOnly)) {
          status = 'past';
        } else if (dayDateOnly.isAfter(nowDateOnly)) {
          status = 'future';
        } else {
          status = 'today';
        }

        week.add(
          locale == const Locale('ar')
              ? {
                  'ar': shortArabicDays[i],
                  'date': date,
                  'status': status,
                  'fullDate': DateFormat('yyyy-MM-dd').format(fullDate),
                }
              : {
                  'en': shortEnglishDays[i],
                  'date': date,
                  'status': status,
                  'fullDate': DateFormat('yyyy-MM-dd').format(fullDate),
                },
        );
      }

      allWeeks.add(week.toList());
    }
    return allWeeks.toList();
  }
}
