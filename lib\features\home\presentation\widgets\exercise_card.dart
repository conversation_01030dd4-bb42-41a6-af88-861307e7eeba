import 'dart:async';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:cal/core/local_models/exercise_model/exercise_model.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/features/home/<USER>/widgets/food_card/shimmer_line.dart';
import 'package:cal/features/home/<USER>/widgets/food_card/loading_text_animation.dart';
import 'package:cal/generated/locale_keys.g.dart';

class ExerciseCard extends StatefulWidget {
  const ExerciseCard({
    super.key,
    required this.exerciseModel,
    required this.onDelete,
    this.onRetry,
  });

  final ExerciseModel exerciseModel;
  final VoidCallback onDelete;
  final VoidCallback? onRetry;

  @override
  State<ExerciseCard> createState() => _ExerciseCardState();
}

class _ExerciseCardState extends State<ExerciseCard> with SingleTickerProviderStateMixin {
  double _progress = 0.0;
  Timer? _successTimer;
  late AnimationController _controller;
  late Animation<double> _animatedProgress;
  bool _isCompleting = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimation();

    if (widget.exerciseModel.isLoading) {
      _controller.forward();
    }
  }

  void _initializeAnimation() {
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    );

    _animatedProgress = Tween<double>(begin: 0.0, end: 0.9).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOutCubic,
      ),
    )..addListener(() {
        if (mounted && !_isCompleting) {
          setState(() {
            _progress = _animatedProgress.value;
          });
        }
      });
  }

  @override
  void didUpdateWidget(covariant ExerciseCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (!oldWidget.exerciseModel.isLoading && widget.exerciseModel.isLoading) {
      _progress = 0.0;
      _controller.reset();
      _controller.forward();
    }

    if (oldWidget.exerciseModel.isLoading && !widget.exerciseModel.isLoading) {
      _controller.stop();
      _animateToCompletion();
    }
  }

  void _animateToCompletion() {
    _isCompleting = true;
    const targetProgress = 1.0;
    const steps = 25;
    const stepDuration = Duration(milliseconds: 30);

    final startProgress = _progress;
    final progressDifference = targetProgress - startProgress;
    final progressPerStep = progressDifference / steps;

    int currentStep = 0;

    _successTimer = Timer.periodic(stepDuration, (timer) {
      if (currentStep < steps) {
        setState(() {
          _progress = startProgress + (progressPerStep * (currentStep + 1));
        });
        currentStep++;
      } else {
        setState(() {
          _progress = targetProgress;
        });
        timer.cancel();

        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            setState(() {
              _isCompleting = false;
              _progress = 0.0;
            });
          }
        });
      }
    });
  }

  Widget _buildProgressIndicator(BuildContext context) {
    return Positioned(
      child: SizedBox(
        width: 85,
        height: 85,
        child: Stack(
          alignment: Alignment.center,
          children: [
            CircularProgressIndicator(
              value: _progress,
              constraints: const BoxConstraints(minHeight: 75, minWidth: 75),
              strokeWidth: 8,
              backgroundColor: context.onPrimaryColor.withAlpha(25),
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            Text(
              '${(_progress * 100).round()}%',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 13,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeChip(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: context.onPrimaryColor,
      ),
      padding: const EdgeInsets.all(5),
      child: _isCompleting
          ? const ShimmerLine(width: 50)
          : AppText.labelMedium(
              DateFormat('h:mm a', 'en').format(widget.exerciseModel.date!),
              color: context.onSecondary,
              fontWeight: FontWeight.w500,
            ),
    );
  }

  Widget _buildCaloriesRow(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (!_isCompleting) ...[
          const Padding(
            padding: EdgeInsets.only(bottom: 8),
            child: AppImage.asset(Assets.imagesCals),
          ),
          const SizedBox(width: 5),
          AppText.bodyMedium(
            widget.exerciseModel.calories?.toString() ?? '0',
            color: context.onSecondary,
            fontWeight: FontWeight.bold,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(width: 5),
          AppText.bodyMedium(
            LocaleKeys.home_calorie.tr(),
            color: context.onSecondary,
            fontWeight: FontWeight.bold,
          ),
          const Spacer(),
        ] else
          const ShimmerLine(width: 50),
      ],
    );
  }

  @override
  void dispose() {
    _successTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  bool get _shouldShowProgress {
    return widget.exerciseModel.isLoading;
  }

  @override
  Widget build(BuildContext context) {
    return Slidable(
      key: ValueKey(
          "${widget.exerciseModel.id}_${widget.exerciseModel.date}_${widget.exerciseModel.isLoading}_${widget.exerciseModel.hasError}"),
      endActionPane: _buildActionPane(),
      child: _buildCardContainer(context),
    );
  }

  ActionPane _buildActionPane() {
    return ActionPane(
      motion: const DrawerMotion(),
      extentRatio: 0.25,
      children: [
        SlidableAction(
          onPressed: (_) => widget.onDelete(),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          icon: Icons.delete,
          label: LocaleKeys.home_delete.tr(),
          borderRadius: BorderRadius.circular(20),
        ),
      ],
    );
  }

  Widget _buildCardContainer(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: const Color(0xfffefefe),
        boxShadow: [
          BoxShadow(
            color: context.onSecondary.withAlpha(35),
            offset: const Offset(-2, 3),
            blurRadius: 10,
          ),
        ],
      ),
      child: Row(
        children: [
          _buildExerciseIcon(context),
          Expanded(
            child: Padding(
              padding: const EdgeInsetsDirectional.symmetric(horizontal: 10),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!widget.exerciseModel.hasError) ...[
                    _buildHeaderRow(context),
                    const SizedBox(height: 3),
                  ],
                  if (!widget.exerciseModel.hasError) ...[_buildCaloriesRow(context), const SizedBox(height: 4)],
                  const SizedBox(height: 4),
                  _buildTimeAndIntensityRow(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseIcon(BuildContext context) {
    IconData iconData;
    Color iconColor;

    // Show error or loading state icons
    if (widget.exerciseModel.hasError) {
      iconData = Icons.error_outline;
      iconColor = Colors.red;
    } else if (widget.exerciseModel.isLoading) {
      iconData = Icons.fitness_center; // Default icon while loading
      iconColor = Colors.grey;
    } else {
      // Determine icon based on exercise type
      final exerciseType = widget.exerciseModel.typeEnglish?.toLowerCase() ?? '';
      switch (exerciseType) {
        case 'run':
        case 'running':
          iconData = Icons.directions_run;
          iconColor = Colors.orange;
          break;
        case 'weight_lifting':
        case 'weightlifting':
          iconData = Icons.fitness_center;
          iconColor = Colors.blue;
          break;
        case 'cycling':
          iconData = Icons.directions_bike;
          iconColor = Colors.green;
          break;
        case 'swimming':
          iconData = Icons.pool;
          iconColor = Colors.cyan;
          break;
        case 'yoga':
          iconData = Icons.self_improvement;
          iconColor = Colors.purple;
          break;
        default:
          iconData = Icons.sports_gymnastics;
          iconColor = Colors.grey;
      }
    }

    return Container(
      width: 117,
      height: 107,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: iconColor.withAlpha(20),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background icon (dimmed during loading)
          Opacity(
            opacity: _shouldShowProgress ? 0.3 : 1.0,
            child: Icon(
              iconData,
              size: 40,
              color: iconColor,
            ),
          ),
          // Progress indicator during loading
          if (_shouldShowProgress)
            SizedBox(
              width: 85,
              height: 85,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  _buildProgressIndicator(context),
                ],
              ),
            ),
          // Retry button for errors
          if (widget.exerciseModel.hasError && widget.onRetry != null && !_shouldShowProgress)
            GestureDetector(
              onTap: widget.onRetry,
              child: const Icon(
                Icons.refresh,
                color: Colors.red,
                size: 40,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHeaderRow(BuildContext context) {
    if (_shouldShowProgress) {
      return const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LoadingTextAnimation(),
          SizedBox(height: 12),
          ShimmerLine(),
        ],
      );
    }

    final exerciseName = widget.exerciseModel.typeArabic ?? widget.exerciseModel.typeEnglish ?? 'تمرين';

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: AppText.bodyMedium(
            textAlign: TextAlign.start,
            exerciseName,
            color: context.onSecondary,
            fontWeight: FontWeight.w500,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        _buildTimeChip(context),
      ],
    );
  }

  // Widget _buildDetailsRow(BuildContext context) {
  //   if (_shouldShowProgress) {
  //     return const ShimmerLine(width: 120);
  //   }

  //   return Row(
  //     children: [
  //       if (widget.exerciseModel.duration != null) ...[
  //         Icon(
  //           Icons.access_time,
  //           size: 16,
  //           color: context.onSecondary.withAlpha(130),
  //         ),
  //         const SizedBox(width: 4),
  //         AppText.bodyMedium(
  //           '${widget.exerciseModel.duration} دقيقة',
  //           color: context.onSecondary.withAlpha(220),
  //         ),
  //       ],
  //     ],
  //   );
  // }

  Widget _buildTimeAndIntensityRow(BuildContext context) {
    if (_shouldShowProgress) {
      return const Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ShimmerLine(width: 50),
        ],
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        if (widget.exerciseModel.intensity != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: _getIntensityColor(widget.exerciseModel.intensity!).withAlpha(25),
            ),
            child: AppText.bodySmall(
              _getIntensityText(widget.exerciseModel.intensity!),
              color: _getIntensityColor(widget.exerciseModel.intensity!),
              fontWeight: FontWeight.w800,
            ),
          ),
      ],
    );
  }

  Color _getIntensityColor(String intensity) {
    switch (intensity.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'mid':
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _getIntensityText(String intensity) {
    switch (intensity.toLowerCase()) {
      case 'high':
        return 'عالي الشدة';
      case 'mid':
      case 'medium':
        return 'متوسطة الشدة';
      case 'low':
        return 'منخفض الشدة';
      default:
        return intensity;
    }
  }
}
