{"buildFiles": ["D:\\SDK\\flutter_windows_3.29.2-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\SDK\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter\\cal\\android\\app\\.cxx\\RelWithDebInfo\\20123p1b\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\SDK\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter\\cal\\android\\app\\.cxx\\RelWithDebInfo\\20123p1b\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\SDK\\android-sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\SDK\\android-sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}