import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

TextTheme textTheme = TextTheme(
  // Display
  displayLarge: GoogleFonts.tajawal(fontSize: 40.0, fontWeight: FontWeight.w400, height: 0),
  displayMedium: GoogleFonts.tajawal(fontSize: 35.0, fontWeight: FontWeight.w400, height: 0),
  displaySmall: GoogleFonts.tajawal(fontSize: 30.0, fontWeight: FontWeight.w400, height: 0),

  // Headline
  headlineLarge: GoogleFonts.tajawal(fontSize: 25, fontWeight: FontWeight.w400, height: 0),
  headlineMedium: GoogleFonts.tajawal(fontSize: 23.0, fontWeight: FontWeight.w400, height: 0),
  headlineSmall: GoogleFonts.tajawal(fontSize: 21.0, fontWeight: FontWeight.w200, height: 0),

  // Title
  titleLarge: GoogleFonts.tajawal(fontSize: 22.0, fontWeight: FontWeight.w700, height: 0),
  titleMedium: GoogleFonts.tajawal(fontSize: 20.0, fontWeight: FontWeight.w700, height: 0),
  titleSmall: GoogleFonts.tajawal(fontSize: 18.0, fontWeight: FontWeight.w500, height: 0),

  // Body
  bodyLarge: GoogleFonts.tajawal(fontSize: 17.0, fontWeight: FontWeight.w500, height: 0),
  bodyMedium: GoogleFonts.tajawal(fontSize: 16.0, fontWeight: FontWeight.w400, height: 0),
  bodySmall: GoogleFonts.tajawal(fontSize: 15.0, fontWeight: FontWeight.w400, height: 0),

  // Label
  labelLarge: GoogleFonts.tajawal(fontSize: 14.0, fontWeight: FontWeight.w300, height: 0),
  labelMedium: GoogleFonts.tajawal(fontSize: 12.0, fontWeight: FontWeight.w300, height: 0),
  labelSmall: GoogleFonts.tajawal(fontSize: 10.0, fontWeight: FontWeight.w300, height: 0),
);
