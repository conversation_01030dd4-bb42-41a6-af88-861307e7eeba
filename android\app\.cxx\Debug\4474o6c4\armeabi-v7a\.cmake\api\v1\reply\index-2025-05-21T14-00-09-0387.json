{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/SDK/android-sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/SDK/android-sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/SDK/android-sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/SDK/android-sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-173a530247cdd8b13bee.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-8ce866e5c8b240158e64.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-70fd352f4a0ce8b5a794.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-8ce866e5c8b240158e64.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-70fd352f4a0ce8b5a794.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-173a530247cdd8b13bee.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}