part of 'food_database_bloc.dart';

enum FoodDatabaseStatus { initial, loading, success, failure }

enum BlocStatus { initial, loading, success, error }

// ignore: must_be_immutable
class FoodDatabaseState extends Equatable {
  FoodDatabaseStatus status;
  List<DatabaseFoodModel> foodList;
  List<DatabaseFoodModel> recentFoodList;
  List<DatabaseFoodModel> myMealsList;
  List<DatabaseFoodModel> myFavoriteList;
  List<DatabaseFoodModel> databaseFoodList;
  List<DatabaseFoodModel> selectedFoods;
  DatabaseFoodModel? myMeal;
  String? errorMessage;

  int? postToLogStatusCode;

  CommonRemoteFoodModel? createMeal;

  List<RemoteFoodDatabaseModel>? searchedFood;
  BlocStatus? searchedFoodStatus;

  FoodDatabaseState({
    this.status = FoodDatabaseStatus.initial,
    this.foodList = const [],
    this.recentFoodList = const [],
    this.databaseFoodList = const [],
    this.myMealsList = const [],
    this.myFavoriteList = const [],
    this.selectedFoods = const [],
    this.errorMessage,
    this.searchedFood,
    this.searchedFoodStatus,
    this.myMeal,
    this.postToLogStatusCode,
    this.createMeal,
  });

  FoodDatabaseState copyWith({
    FoodDatabaseStatus? status,
    List<DatabaseFoodModel>? foodList,
    List<DatabaseFoodModel>? recentFoodList,
    List<DatabaseFoodModel>? myMealsList,
    List<DatabaseFoodModel>? myFavoriteList,
    List<DatabaseFoodModel>? databaseFoodList,
    List<DatabaseFoodModel>? selectedFoods,
    DatabaseFoodModel? myMeal,
    String? errorMessage,
    List<RemoteFoodDatabaseModel>? searchedFood,
    BlocStatus? searchedFoodStatus,
    int? postToLogStatusCode,
    CommonRemoteFoodModel? createMeal,
  }) {
    return FoodDatabaseState(
      status: status ?? this.status,
      foodList: foodList ?? this.foodList,
      recentFoodList: recentFoodList ?? this.recentFoodList,
      selectedFoods: selectedFoods ?? this.selectedFoods,
      myMealsList: myMealsList ?? this.myMealsList,
      myFavoriteList: myFavoriteList ?? this.myFavoriteList,
      databaseFoodList: databaseFoodList ?? this.databaseFoodList,
      errorMessage: errorMessage ?? this.errorMessage,
      searchedFood: searchedFood ?? this.searchedFood,
      searchedFoodStatus: searchedFoodStatus ?? this.searchedFoodStatus,
      myMeal: myMeal ?? this.myMeal,
      postToLogStatusCode: postToLogStatusCode ?? this.postToLogStatusCode,
      createMeal: createMeal ?? this.createMeal,
    );
  }

  @override
  List<Object?> get props => [
        status,
        foodList,
        recentFoodList,
        databaseFoodList,
        errorMessage,
        myFavoriteList,
        myMealsList,
        selectedFoods,
        searchedFood,
        searchedFoodStatus,
        myMeal,
      ];
}
