import 'package:cal/features/quick_actions/food_database/presentation/widgets/food_database_card.dart';
import 'package:flutter/material.dart';

class MyFoodsContent extends StatelessWidget {
  const MyFoodsContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 19),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) => const FoodDatabaseCard(
            title: 'نسكافيه',
            cals: '85',
            description: "وجبة غنية بالبروتين وقليلة الدهون، تتكوّن من صدر دجاج مشوي متبل بالأعشاب",
          ),
          separatorBuilder: (context, index) => const SizedBox(height: 16),
          itemCount: 10,
        ),
      ],
    );
  }
}
