import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';
import '../models/auth_token_model.dart';

// Local Data Source for Authentication
abstract class AuthenticationLocalDataSource {
  Future<void> saveUser(UserModel user);
  Future<UserModel?> getUser();
  Future<void> saveToken(AuthTokenModel token);
  Future<AuthTokenModel?> getToken();
  Future<void> clearAll();
}

class AuthenticationLocalDataSourceImpl implements AuthenticationLocalDataSource {
  static const String _userKey = 'user';
  static const String _tokenKey = 'token';
  
  final FlutterSecureStorage _secureStorage;

  AuthenticationLocalDataSourceImpl(this._secureStorage);

  @override
  Future<void> saveUser(UserModel user) async {
    final userJson = jsonEncode(user.toJson());
    await _secureStorage.write(key: _userKey, value: userJson);
  }

  @override
  Future<UserModel?> getUser() async {
    final userJson = await _secureStorage.read(key: _userKey);
    if (userJson != null) {
      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      return UserModel.fromJson(userMap);
    }
    return null;
  }

  @override
  Future<void> saveToken(AuthTokenModel token) async {
    final tokenJson = jsonEncode(token.toJson());
    await _secureStorage.write(key: _tokenKey, value: tokenJson);
  }

  @override
  Future<AuthTokenModel?> getToken() async {
    final tokenJson = await _secureStorage.read(key: _tokenKey);
    if (tokenJson != null) {
      final tokenMap = jsonDecode(tokenJson) as Map<String, dynamic>;
      return AuthTokenModel.fromJson(tokenMap);
    }
    return null;
  }

  @override
  Future<void> clearAll() async {
    await _secureStorage.delete(key: _userKey);
    await _secureStorage.delete(key: _tokenKey);
  }
}

