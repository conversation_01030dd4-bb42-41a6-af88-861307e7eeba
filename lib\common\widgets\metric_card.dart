import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:flutter/material.dart';

import 'app_image.dart';

class HorizontalMetricCard extends StatelessWidget {
  final String title;
  final String description;
  final String? icon;
  final Color color;
  final double progress;
  final VoidCallback? onTap;
  final Duration animationDuration;
  final double strokedWidth;
  final double progressHeight;
  final double progressWidth;

  const HorizontalMetricCard({
    super.key,
    required this.title,
    required this.description,
    this.icon,
    required this.color,
    this.strokedWidth = 7,
    required this.progress,
    this.onTap,
    this.animationDuration = const Duration(milliseconds: 800),
    required this.progressHeight,
    required this.progressWidth,
  });

  @override
  Widget build(BuildContext context) {
    return AppGestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: context.colorScheme.onPrimary,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: context.onSecondary.withAlpha(41),
              offset: const Offset(-2, 4),
              blurRadius: 18,
            ),
          ],
        ),
        padding: const EdgeInsetsDirectional.symmetric(horizontal: 30, vertical: 24),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: _children(context),
        ),
      ),
    );
  }

  List<Widget> _children(BuildContext context) => [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 1),
              AnimatedFlipCounter(
                padding: const EdgeInsets.symmetric(vertical: 3),
                duration: const Duration(milliseconds: 1100),
                value: int.parse(title),
                curve: Curves.easeInOutCubic,
                textStyle: context.textTheme.bodyLarge!.copyWith(
                  fontWeight: FontWeight.w800,
                  color: context.primaryColor,
                  fontSize: 40,
                  letterSpacing: 0,
                  height: 0,
                ),
              ),
              Text(
                description,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: context.textTheme.displayMedium!.copyWith(
                  fontWeight: FontWeight.w800,
                  color: context.onSecondary.withAlpha(150),
                  fontSize: context.screenHeight * 0.020,
                  height: 0,
                ),
              ),
              // Text(
              //   description,
              //   maxLines: 1,
              //   overflow: TextOverflow.ellipsis,
              //   style: context.textTheme.displayMedium!.copyWith(
              //     fontWeight: FontWeight.w900,
              //     color: context.onSecondary.withAlpha(150),
              //     fontSize: 16,
              //   ),
              // ),
            ],
          ),
        ),
        Stack(
          alignment: Alignment.center,
          children: [
            TweenAnimationBuilder<double>(
              duration: animationDuration,
              curve: Curves.easeInOut,
              tween: Tween<double>(
                begin: 0,
                end: progress,
              ),
              builder: (context, value, _) {
                return SizedBox(
                  width: progressWidth,
                  height: progressHeight,
                  child: CircularProgressIndicator(
                    value: value,
                    strokeWidth: strokedWidth,
                    backgroundColor: context.onPrimaryContainer.withAlpha(150),
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                  ),
                );
              },
            ),
            CircleAvatar(
              radius: 17,
              backgroundColor: context.onPrimaryContainer.withAlpha(150),
              child: icon == null
                  ? const SizedBox.shrink()
                  : AppImage.asset(
                      icon!,
                      size: 18,
                    ),
            ),
          ],
        ),
      ];
}

class VerticalMetricCard extends StatelessWidget {
  final String title;
  final String description;
  final String? icon;
  final Color color;
  final double progress;
  final VoidCallback? onTap;
  final Duration animationDuration;
  final double strokedWidth;
  final double progressHeight;
  final double progressWidth;

  const VerticalMetricCard({
    super.key,
    required this.title,
    required this.description,
    this.icon,
    required this.color,
    this.strokedWidth = 7,
    required this.progress,
    this.onTap,
    this.animationDuration = const Duration(milliseconds: 800),
    required this.progressHeight,
    required this.progressWidth,
  });

  @override
  Widget build(BuildContext context) {
    return AppGestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: context.colorScheme.onPrimary,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: context.onSecondary.withAlpha(41),
              offset: const Offset(-2, 4),
              blurRadius: 18,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: _children(context),
        ),
      ),
    );
  }

  List<Widget> _children(BuildContext context) => [
        Column(
          children: [
            const SizedBox(height: 1),
            Padding(
              padding: const EdgeInsetsDirectional.only(start: 10, end: 10, top: 10),
              child: Row(
                textDirection: TextDirection.rtl,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "g",
                    style: context.textTheme.bodyLarge!.copyWith(
                      fontWeight: FontWeight.w800,
                      color: context.primaryColor,
                      fontSize: 30,
                      letterSpacing: 0,
                      height: 1,
                    ),
                  ),
                  AnimatedFlipCounter(
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    duration: const Duration(milliseconds: 1100),
                    value: int.parse(title),
                    curve: Curves.easeInOutCubic,
                    textStyle: context.textTheme.bodyLarge!.copyWith(
                      fontWeight: FontWeight.w800,
                      color: context.primaryColor,
                      fontSize: 30,
                      letterSpacing: 0,
                      height: 1,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: context.textTheme.displayMedium!.copyWith(
                fontWeight: FontWeight.w900,
                color: context.onSecondary.withAlpha(150),
                fontSize: context.screenHeight * 0.0175,
                height: 0,
              ),
            ),
            const SizedBox(height: 10),
          ],
        ),
        Stack(
          alignment: Alignment.center,
          children: [
            TweenAnimationBuilder<double>(
              duration: animationDuration,
              curve: Curves.easeInOut,
              tween: Tween<double>(
                begin: 0,
                end: progress,
              ),
              builder: (context, value, _) {
                return SizedBox(
                  width: progressWidth,
                  height: progressHeight,
                  child: CircularProgressIndicator(
                    value: value,
                    strokeWidth: strokedWidth,
                    backgroundColor: context.onPrimaryContainer.withAlpha(150),
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                  ),
                );
              },
            ),
            CircleAvatar(
              radius: 17,
              backgroundColor: context.onPrimaryContainer.withAlpha(150),
              child: icon == null
                  ? const SizedBox.shrink()
                  : AppImage.asset(
                      icon!,
                      size: 18,
                    ),
            ),
          ],
        ),
        const SizedBox(height: 20),
      ];
}

double calculateSafeProgress(double? totalCalories, double? consumedCalories) {
  if (totalCalories == null || consumedCalories == null || totalCalories <= 0) {
    return 0.0;
  }

  final raw = consumedCalories / totalCalories;

  if (raw.isNaN || raw.isInfinite) {
    return 0.0;
  }

  return raw.clamp(0.0, 1.0);
}
