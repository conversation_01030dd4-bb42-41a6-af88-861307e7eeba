<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>996337025817-mpjsgu8f9bcttuunk0o6gg9og6g6quul.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.996337025817-mpjsgu8f9bcttuunk0o6gg9og6g6quul</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>996337025817-099bi6dr1uqtvuau7enualkdqh939otv.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyD6C3RTajq4_3mq9kvrLUsqhVT4NmG6lOo</string>
	<key>GCM_SENDER_ID</key>
	<string>996337025817</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.raizer.orangeai</string>
	<key>PROJECT_ID</key>
	<string>orange-ai-fe389</string>
	<key>STORAGE_BUCKET</key>
	<string>orange-ai-fe389.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:996337025817:ios:0eeed98221aa4289a27b88</string>
</dict>
</plist>