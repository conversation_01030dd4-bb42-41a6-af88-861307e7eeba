import 'package:cal/features/subscriptions/enums.dart';
import 'package:equatable/equatable.dart';

class SubscriptionPlanEntity extends Equatable {
  final String id;
  final String title;
  final String description;
  final double price;
  final SubscriptionPlanType type;
  final List<String> features;

  const SubscriptionPlanEntity({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.type,
    required this.features,
  });

  @override
  List<Object?> get props => [id, title, description, price, type, features];
}
