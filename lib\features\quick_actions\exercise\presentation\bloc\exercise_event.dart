
part of 'exercise_bloc.dart';

abstract class ExerciseEvent extends Equatable {
  const ExerciseEvent();

  @override
  List<Object> get props => [];
}

class SaveExercise extends ExerciseEvent {
  final ExerciseSaveModel exercise;

  const SaveExercise({required this.exercise});

  @override
  List<Object> get props => [exercise];
}

class SaveExerciseAi extends ExerciseEvent {
  final ExerciseSaveAiModel exercise;

  const SaveExerciseAi({required this.exercise});

  @override
  List<Object> get props => [exercise];
}

class RetryExercise extends ExerciseEvent {
  const RetryExercise();

  @override
  List<Object> get props => [];
}


