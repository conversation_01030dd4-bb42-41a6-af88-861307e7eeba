// part of 'recent_food_bloc.dart';

// enum RecentFoodStatus { initial, loading, success, failure }

// class RecentFoodState extends Equatable {
//   final RecentFoodStatus status;
//   final List<FoodModel> foodList;
//   final String? errorMessage;

//   const RecentFoodState({
//     this.status = RecentFoodStatus.initial,
//     this.foodList = const [],
//     this.errorMessage,
//   });

//   RecentFoodState copyWith({
//     RecentFoodStatus? status,
//     List<FoodModel>? foodList,
//     String? errorMessage,
//   }) {
//     return RecentFoodState(
//       status: status ?? this.status,
//       foodList: foodList ?? this.foodList,
//       errorMessage: errorMessage ?? this.errorMessage,
//     );
//   }

//   @override
//   List<Object?> get props => [status, foodList, errorMessage];
// }
