import 'package:cal/core/local_models/exercise_model/exercise_model.dart';
import 'package:cal/features/home/<USER>/repositories/exercise_repository.dart';
import 'package:injectable/injectable.dart';

@injectable
class DeleteExerciseUseCase {
  final HomeExerciseRepository repository;

  DeleteExerciseUseCase(this.repository);

  Future<void> call(ExerciseModel exercise) async {
    await repository.deleteExercise(exercise);
  }
}
