import 'package:equatable/equatable.dart';
import 'package:isar/isar.dart';

part 'streak_model.g.dart';


@Collection(inheritance: false)
// ignore: must_be_immutable
class StreakModel extends Equatable {

  Id id = Isar.autoIncrement;

  StreakModel({
    required this.hasAction,
    required this.streakDate,
  });

  final bool hasAction;
  final DateTime streakDate;

  @override
  @ignore
  List<Object?> get props => [hasAction, streakDate];
}
