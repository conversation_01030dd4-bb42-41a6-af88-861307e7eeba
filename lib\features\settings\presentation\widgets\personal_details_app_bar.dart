import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';


class PersonalDetailsAppBar extends StatelessWidget {
  const PersonalDetailsAppBar({super.key, required this.title});

  final String title;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: context.locale.languageCode != 'ar' ? Alignment.centerLeft : Alignment.centerRight,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: context.textTheme.bodyLarge!.copyWith(
                color: context.primaryColor,
                fontWeight: FontWeight.w500,
              ),
            )
          ],
        ),
        IconButton(
          onPressed: () {
            context.pop();
          },
          icon: const Icon(
            Icons.arrow_back,
          ),
        ),
      ],
    );
  }
}
