import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/theme/text_theme.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:flutter/material.dart';

class EnterYourFoodContainer extends StatelessWidget {
  final void Function() onPressed;
  final String text;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final Color? borderColor;
  final bool isFlexible;
  final double circularRadius;
  final double? height;
  final double? width;
  final Widget icon;
  final MainAxisAlignment mainAxisAlignment;
  final bool hasBorder;
  final double verticalaPadding;

  const EnterYourFoodContainer({
    required this.onPressed,
    required this.text,
    required this.icon,
    this.textStyle,
    this.backgroundColor,
    this.borderColor,
    this.isFlexible = false,
    this.circularRadius = 5.0,
    this.height,
    this.width,
    this.mainAxisAlignment = MainAxisAlignment.center,
    this.hasBorder = true,
    this.verticalaPadding = 12.0,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return AppGestureDetector(
      onTap: onPressed,
      child: Container(
        height: height,
        width: width,
        padding: !isFlexible ? EdgeInsets.symmetric(horizontal: 25.0, vertical: verticalaPadding) : null,
        decoration: BoxDecoration(
          color: context.onPrimaryColor,
          border: hasBorder
              ? Border.all(color: borderColor ?? context.onSecondary, width: 1)
              : Border.all(color: borderColor ?? context.onSecondary.withAlpha(50), width: 1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(3),
          child: Row(
            spacing: 12,
            mainAxisAlignment: mainAxisAlignment,
            children: [
              Text(
                text,
                textAlign: TextAlign.center,
                style: textTheme.bodyMedium!.copyWith(
                    color: Theme.of(context).colorScheme.onSecondary, fontWeight: FontWeight.bold, fontSize: 18),
              ),
              icon
            ],
          ),
        ),
      ),
    );
  }
}
