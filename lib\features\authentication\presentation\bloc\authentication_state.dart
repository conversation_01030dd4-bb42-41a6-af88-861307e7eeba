part of 'authentication_bloc.dart';

// Authentication Status Enum
enum AuthenticationStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

// Authentication State
class AuthenticationState {
  final AuthenticationStatus status;
  final UserEntity? user;
  final String? errorMessage;

  const AuthenticationState({
    this.status = AuthenticationStatus.initial,
    this.user,
    this.errorMessage,
  });

  AuthenticationState copyWith({
    AuthenticationStatus? status,
    UserEntity? user,
    String? errorMessage,
  }) {
    return AuthenticationState(
      status: status ?? this.status,
      user: user ?? this.user,
      errorMessage: errorMessage,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthenticationState &&
        other.status == status &&
        other.user == user &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode {
    return status.hashCode ^
        user.hashCode ^
        errorMessage.hashCode;
  }

  @override
  String toString() {
    return 'AuthenticationState(status: $status, user: $user, errorMessage: $errorMessage)';
  }
}

