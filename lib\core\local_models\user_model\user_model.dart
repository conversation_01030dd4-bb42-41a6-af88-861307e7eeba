import 'package:equatable/equatable.dart';
import 'package:isar/isar.dart';

part 'user_model.g.dart';

@Collection(inheritance: false)
// ignore: must_be_immutable
class UserModel extends Equatable {
  Id id = Isar.autoIncrement;

  String? gender;
  String? exerciseFrequency;
  String? height;
  String? weight;
  DateTime? birthDate;
  String? activity;
  String? targetWeight;
  String? weeklyTarget;
  String? goal;
  String? hearingAboutUs;

  double? weightChangeRate;

  String? targetCalories;
  String? targetFat;
  String? targetProtein;
  String? targetCarbs;

  UserModel({
    this.gender,
    this.exerciseFrequency,
    this.height,
    this.weight,
    this.birthDate,
    this.activity,
    this.targetWeight,
    this.weeklyTarget,
    this.goal,
    this.hearingAboutUs,
    this.weightChangeRate,
    this.targetCalories,
    this.targetFat,
    this.targetProtein,
    this.targetCarbs,
  });

  UserModel copyWith({
    String? gender,
    String? exerciseFrequency,
    String? height,
    String? weight,
    DateTime? birthDate,
    String? activity,
    String? targetWeight,
    String? weeklyTarget,
    String? goal,
    String? hearingAboutUs,
    double? weightChangeRate,
    String? targetCalories,
    String? targetFat,
    String? targetProtein,
    String? targetCarbs,
  }) {
    return UserModel(
      gender: gender ?? this.gender,
      exerciseFrequency: exerciseFrequency ?? this.exerciseFrequency,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      birthDate: birthDate ?? this.birthDate,
      activity: activity ?? this.activity,
      targetWeight: targetWeight ?? this.targetWeight,
      weeklyTarget: weeklyTarget ?? this.weeklyTarget,
      goal: goal ?? this.goal,
      hearingAboutUs: hearingAboutUs ?? this.hearingAboutUs,
      weightChangeRate: weightChangeRate ?? this.weightChangeRate,
      targetCalories: targetCalories ?? this.targetCalories,
      targetFat: targetFat ?? this.targetFat,
      targetProtein: targetProtein ?? this.targetProtein,
      targetCarbs: targetCarbs ?? this.targetCarbs,
    );
  }

  Map<String, dynamic> toJson() => {
        "gender": gender,
        "exerciseFrequency": exerciseFrequency,
        "height": height,
        "weight": weight,
        "birthDate": birthDate?.toIso8601String(),
        "activity": activity,
        "targetWeight": targetWeight,
        "weeklyTarget": weeklyTarget,
        "goal": goal,
        "hearingAboutUs": hearingAboutUs,
        "weightChangeRate": weightChangeRate,
        "cals": targetCalories,
        "fat": targetFat,
        "protein": targetProtein,
        "carbs": targetCarbs,
      };

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      gender: json['gender'],
      exerciseFrequency: json['exerciseFrequency'],
      height: json['height'],
      weight: json['weight'],
      birthDate: json['birthDate'] != null ? DateTime.parse(json['birthDate']) : null,
      activity: json['activity'],
      targetWeight: json['targetWeight'],
      weeklyTarget: json['weeklyTarget'],
      goal: json['goal'],
      hearingAboutUs: json['hearingAboutUs'],
      weightChangeRate: (json['weightChangeRate'] as num?)?.toDouble(),
      targetCalories: json['cals'],
      targetFat: json['fat'],
      targetProtein: json['protein'],
      targetCarbs: json['carbs'],
    );
  }

  @override
  @ignore
  List<Object?> get props => [
        gender,
        exerciseFrequency,
        height,
        weight,
        birthDate,
        activity,
        targetWeight,
        weeklyTarget,
        goal,
        hearingAboutUs,
        weightChangeRate,
        targetCalories,
        targetFat,
        targetProtein,
        targetCalories,
      ];
}
