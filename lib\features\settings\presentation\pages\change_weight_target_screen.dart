import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/core/datasources/user_local_data_source.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/core/local_models/user_model/user_model.dart';
import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'package:wheel_slider/wheel_slider.dart';

import '../../../../common/widgets/app_text.dart';
import '../../../../common/widgets/large_button.dart';

class ChangeWeightTargetScreen extends StatefulWidget {
  const ChangeWeightTargetScreen({super.key, required this.model});

  final UserModel model;

  @override
  State<ChangeWeightTargetScreen> createState() => _ChangeWeightTargetScreenState();
}

class _ChangeWeightTargetScreenState extends State<ChangeWeightTargetScreen> {
  double newWeight = 0;

  @override
  void initState() {
    super.initState();

    newWeight = double.parse(widget.model.targetWeight!);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                          borderRadius: BorderRadius.circular(50),
                          onTap: () {
                            context.pop();
                          },
                          child: CircleAvatar(
                            radius: 25,
                            backgroundColor: context.onSecondary.withAlpha(21),
                            child: Icon(
                              Icons.arrow_back,
                              color: context.onSecondary,
                              size: 18,
                            ),
                          ),
                        ),
                        AppText.titleSmall('تغيير الوزن الهدف', color: context.onSecondary, fontWeight: FontWeight.w700),
                        const CircleAvatar(
                          radius: 25,
                          backgroundColor: Colors.transparent,
                          child: Icon(
                            Icons.arrow_back,
                            color: Colors.transparent,
                            size: 18,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: context.screenHeight * .3),
                    Text(
                      'هدفك الجديد',
                      style: context.textTheme.titleSmall!.copyWith(color: context.onSecondary, fontWeight: FontWeight.w700),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    Text(
                      '${newWeight.toStringAsFixed(1)}Kg',
                      style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      height: 80,
                      child: WheelSlider(
                        isVibrate: false,
                        isInfinite: false,
                        totalCount: 8000,
                        initValue: (newWeight * 10).toInt(),
                        onValueChanged: (index) {
                          final double currentWeight = index / 10;
                          setState(() => newWeight = currentWeight);
                        },
                        itemSize: 20,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.symmetric(horizontal: 20),
              child: LargeButton(
                onPressed: () async {
                  await UserLocalDataSource(getIt<Isar>())
                      .saveUserData(widget.model.copyWith(targetWeight: newWeight.toString()))
                      .then((val) {
                    if (context.mounted) context.pop();
                  });
                },
                text: 'Save Changes',
                backgroundColor: context.primaryColor,
                circularRadius: 16,
                textStyle: context.textTheme.bodyMedium!.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
