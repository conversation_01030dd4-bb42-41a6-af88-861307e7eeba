import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Goal Selection Screen
class AchieveGoalVisualaizationContent extends StatefulWidget {
  const AchieveGoalVisualaizationContent({super.key});

  @override
  State<AchieveGoalVisualaizationContent> createState() => _GoalSelectionContentState();
}

class _GoalSelectionContentState extends State<AchieveGoalVisualaizationContent> {
  @override
  void initState() {
    super.initState();
    context.read<OnboardingBloc>().add(RegisterScreenValidation((state) => state.diet != null));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_youre_more_likely_to_achieve_your_goal.tr(),
          centerContent: true,
          contentWidgets: const [
            AnimatedWeightChart(),
          ],
        );
      },
    );
  }
}

class AnimatedWeightChart extends StatefulWidget {
  const AnimatedWeightChart({Key? key}) : super(key: key);

  @override
  State<AnimatedWeightChart> createState() => _AnimatedWeightChartState();
}

class _AnimatedWeightChartState extends State<AnimatedWeightChart> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Start animation automatically
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: context.onSecondary.withAlpha(20),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            LocaleKeys.onboarding_your_weight_changes.tr(),
            textAlign: TextAlign.right,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          // const SizedBox(height: 30),
          SizedBox(
            width: double.infinity,
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return CustomPaint(
                  painter: WeightChartPainter(_animation.value),
                  size: const Size(double.infinity, 250),
                );
              },
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: context.locale.languageCode == 'en'
                ? [
                    Text(
                      "3 ${LocaleKeys.onboarding_days.tr()}",
                      textAlign: TextAlign.right,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      "7 ${LocaleKeys.onboarding_days.tr()}",
                      textAlign: TextAlign.right,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      "30 ${LocaleKeys.onboarding_days.tr()}",
                      textAlign: TextAlign.right,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ]
                : [
                    Text(
                      "3 ${LocaleKeys.onboarding_days.tr()}",
                      textAlign: TextAlign.right,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      "7 ${LocaleKeys.onboarding_days.tr()}",
                      textAlign: TextAlign.right,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      "30 ${LocaleKeys.onboarding_days.tr()}",
                      textAlign: TextAlign.right,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ].reversed.toList(),
          ),
          const SizedBox(height: 20),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: Text(
                  "${LocaleKeys.onboarding_dependeng_on_the_informaions.tr()}:",
                  style: context.textTheme.bodyLarge!.copyWith(fontWeight: FontWeight.w300, color: context.onSecondary.withAlpha(150)),
                  textAlign: TextAlign.center,
                ),
              ),
              Flexible(
                child: Text(
                  LocaleKeys.onboarding_Weight_loss_in_the_first_seven_days_is_slow_but_after_that_you_can_burn_fat_quickly.tr(),
                  textAlign: TextAlign.center,
                  style: context.textTheme.bodyLarge!.copyWith(fontWeight: FontWeight.w300, color: context.onSecondary.withAlpha(150)),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class WeightChartPainter extends CustomPainter {
  final double animationValue;

  WeightChartPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final fillPaint = Paint()
      ..shader = const LinearGradient(
        begin: Alignment.centerRight,
        end: Alignment.centerLeft,
        colors: [
          Color.fromARGB(138, 250, 121, 41),
          Color.fromARGB(96, 235, 235, 235),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.fill;

    final dotPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill
      ..strokeWidth = 5;

    final dotBorderPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill
      ..strokeWidth = 4;

    // Grid Paint Configuration
    final gridPaint = Paint()
      ..color = Colors.grey.withAlpha(77)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;
    // .. = const [2, 2]; // Dotted line pattern

    // Draw horizontal grid lines
    const horizontalLines = 5;
    for (int i = 1; i < horizontalLines; i++) {
      final y = (size.height / horizontalLines) * i + 50;
      canvas.drawLine(
        Offset(32, y), // Start 32px from left
        Offset(size.width - 32, y), // End 32px from right
        gridPaint,
      );
    }

    // Draw vertical grid lines
    final dataPoints = [
      Offset(0.1 * size.width, 0.8 * size.height),
      Offset(0.4 * size.width, 0.7 * size.height),
      Offset(0.7 * size.width, 0.5 * size.height),
      Offset(0.9 * size.width, 0.3 * size.height),
    ];

    for (int i = 1; i < dataPoints.length; i++) {
      final x = dataPoints[i].dx;

      // Shorten vertical lines by adding padding at top/bottom
      const startY = 72.0; // Top padding
      final endY = size.height; // Bottom padding

      canvas.drawLine(
        Offset(x, startY), // Start 32px from top
        Offset(x, endY), // End 32px from bottom
        gridPaint,
      );
    }
    // Create animated path
    final path = Path();
    final fillPath = Path();

    if (animationValue > 0) {
      // Calculate how many points to show based on animation
      final animatedLength = animationValue * (dataPoints.length - 1);
      final completePoints = animatedLength.floor();
      final partialProgress = animatedLength - completePoints;

      // Start the path
      path.moveTo(dataPoints[0].dx, dataPoints[0].dy);
      fillPath.moveTo(dataPoints[0].dx, size.height);
      fillPath.lineTo(dataPoints[0].dx, dataPoints[0].dy);

      // Add complete segments
      for (int i = 0; i < completePoints && i < dataPoints.length - 1; i++) {
        final controlPoint1 = Offset(
          dataPoints[i].dx + (dataPoints[i + 1].dx - dataPoints[i].dx) * 0.3,
          dataPoints[i].dy,
        );
        final controlPoint2 = Offset(
          dataPoints[i].dx + (dataPoints[i + 1].dx - dataPoints[i].dx) * 0.7,
          dataPoints[i + 1].dy,
        );

        path.cubicTo(
          controlPoint1.dx,
          controlPoint1.dy,
          controlPoint2.dx,
          controlPoint2.dy,
          dataPoints[i + 1].dx,
          dataPoints[i + 1].dy,
        );

        fillPath.cubicTo(
          controlPoint1.dx,
          controlPoint1.dy,
          controlPoint2.dx,
          controlPoint2.dy,
          dataPoints[i + 1].dx,
          dataPoints[i + 1].dy,
        );
      }

      // Add partial segment if needed
      if (completePoints < dataPoints.length - 1 && partialProgress > 0) {
        final startPoint = dataPoints[completePoints];
        final endPoint = dataPoints[completePoints + 1];
        final partialEndPoint = Offset(
          startPoint.dx + (endPoint.dx - startPoint.dx) * partialProgress,
          startPoint.dy + (endPoint.dy - startPoint.dy) * partialProgress,
        );

        final controlPoint1 = Offset(
          startPoint.dx + (endPoint.dx - startPoint.dx) * 0.3 * partialProgress,
          startPoint.dy,
        );
        final controlPoint2 = Offset(
          startPoint.dx + (endPoint.dx - startPoint.dx) * 0.7 * partialProgress,
          partialEndPoint.dy,
        );

        path.cubicTo(
          controlPoint1.dx,
          controlPoint1.dy,
          controlPoint2.dx,
          controlPoint2.dy,
          partialEndPoint.dx,
          partialEndPoint.dy,
        );

        fillPath.cubicTo(
          controlPoint1.dx,
          controlPoint1.dy,
          controlPoint2.dx,
          controlPoint2.dy,
          partialEndPoint.dx,
          partialEndPoint.dy,
        );
      }

      // Complete the fill path
      final lastPoint = path.getBounds();
      fillPath.lineTo(lastPoint.right, size.height);
      fillPath.close();

      // Draw the fill
      canvas.drawPath(fillPath, fillPaint);

      // Draw the line
      canvas.drawPath(path, paint);

      // Draw dots for completed points
      for (int i = 0; i <= completePoints && i < dataPoints.length; i++) {
        // Draw white border
        canvas.drawCircle(dataPoints[i], 8, dotBorderPaint);
        // Draw orange dot
        canvas.drawCircle(dataPoints[i], 5, dotPaint);
      }

      // Draw partial dot if needed
      if (completePoints < dataPoints.length - 1 && partialProgress > 0.5) {
        final dotIndex = completePoints + 1;
        if (dotIndex < dataPoints.length) {
          canvas.drawCircle(dataPoints[dotIndex], 8, dotBorderPaint);
          canvas.drawCircle(dataPoints[dotIndex], 5, dotPaint);
        }
      }
    }
  }

  @override
  bool shouldRepaint(WeightChartPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}
