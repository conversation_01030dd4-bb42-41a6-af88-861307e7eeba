import 'package:easy_localization/easy_localization.dart';

import '../../../../../core/local_models/daily_data_model/daily_data_model.dart';
import '../target_progress_chart.dart';

class ProgressHelper {
  static List<ProgressTarget> getTheData(List<DailyUserDataModel> data, int periodIndex) {
    List<ProgressTarget> selectedTargets = [];
    DateTime now = DateTime.now();

    data.sort((a, b) => a.date.compareTo(b.date));

    Map<DateTime, double> dataMap = {for (var item in data) DateTime(item.date.year, item.date.month, item.date.day): item.weight};

    int totalDaysInPeriod;
    DateTime periodStartDate;

    switch (periodIndex) {
      case 0:
        totalDaysInPeriod = 90;
        periodStartDate = now.subtract(Duration(days: totalDaysInPeriod));
        break;
      case 1:
        totalDaysInPeriod = 180;
        periodStartDate = now.subtract(Duration(days: totalDaysInPeriod));
        break;
      case 2:
        totalDaysInPeriod = 365;
        periodStartDate = now.subtract(Duration(days: totalDaysInPeriod));
        break;
      case 3:
        if (data.isEmpty) {
          return [];
        }
        periodStartDate = data.first.date;
        totalDaysInPeriod = now.difference(periodStartDate).inDays;
      default:
        totalDaysInPeriod = 90;
        periodStartDate = now.subtract(Duration(days: totalDaysInPeriod));
    }

    List<DateTime> datesToSelect = [];

    if (totalDaysInPeriod >= 0) {
      double interval = totalDaysInPeriod / 4.0;

      for (int i = 0; i < 5; i++) {
        DateTime dateToAdd = periodStartDate.add(Duration(days: (interval * i).round()));
        datesToSelect.add(dateToAdd);
      }
    } else {
      if (data.length <= 5) {
        datesToSelect = data.map((e) => e.date).toList();
      } else {
        double interval = (data.length - 1) / 4.0;
        for (int i = 0; i < 5; i++) {
          datesToSelect.add(data[(i * interval).round()].date);
        }
      }
    }

    datesToSelect = datesToSelect.toSet().toList();
    datesToSelect.sort((a, b) => a.compareTo(b));

    for (DateTime date in datesToSelect) {
      DateTime normalizedDate = DateTime(date.year, date.month, date.day);
      double weight = dataMap[normalizedDate] ?? 0.0;
      selectedTargets.add(ProgressTarget(DateFormat('MMM dd').format(date), weight));
    }

    return selectedTargets;
  }

  static DateTime getLastSaturday() {
    DateTime now = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);
    int daysSinceSaturday = (now.weekday + 1) % 7;
    return now.subtract(Duration(days: daysSinceSaturday));
  }
}
