import 'package:cal/core/local_models/user_model/user_model.dart';
import 'package:cal/features/onboarding/domain/repositories/onboarding_repository.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class GetUserLocalUserCase {

  final OnboardingRepository onboardingRepository;
  GetUserLocalUserCase({required this.onboardingRepository});

  Future<UserModel?> getUserData(UserModel user) async {
    return onboardingRepository.getUserData(user);
  }

}