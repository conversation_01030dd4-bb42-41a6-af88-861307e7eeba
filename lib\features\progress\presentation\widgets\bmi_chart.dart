import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class BmiChart extends StatelessWidget {
  BmiChart({super.key, required this.bmi});

  final double bmi;

  List<String> get title => [
        LocaleKeys.progress_underweight.tr(),
        LocaleKeys.progress_healthy.tr(),
        LocaleKeys.progress_overweight.tr(),
        LocaleKeys.progress_obese.tr(),
      ];

  final List<Color> colors = [
    const Color(0xff4A90E2),
    const Color(0xff27AE60),
    const Color(0xffF1C40F),
    const Color(0xffE74C3C),
  ];

  String getBmiCategory(double bmi) {
    if (bmi < 18.5) return LocaleKeys.progress_underweight.tr();
    if (bmi < 25) return LocaleKeys.progress_healthy.tr();
    if (bmi < 30) return LocaleKeys.progress_overweight.tr();
    return LocaleKeys.progress_obese.tr();
  }

  double getCustomBmiPercent(double bmi) {
    if (bmi < 0) return 0;

    if (bmi <= 18.5) {
      return (bmi / 18.5) * 25;
    } else if (bmi <= 24.9) {
      return 25 + ((bmi - 18.5) / (24.9 - 18.5)) * 25;
    } else if (bmi <= 29.9) {
      return 50 + ((bmi - 25) / (29.9 - 25)) * 25;
    } else if (bmi <= 40) {
      return 75 + ((bmi - 30) / (40 - 30)) * 25;
    } else {
      return 100;
    }
  }


  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.onPrimaryColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: context.onSecondary.withAlpha(51),
            blurRadius: 10,
            offset: const Offset(.2, 4),
          )
        ],
      ),
      padding: const EdgeInsetsDirectional.only(start: 10, end: 10, top: 15, bottom: 26),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          AppText.titleLarge(
            LocaleKeys.progress_bmi.tr(),
            fontWeight: FontWeight.bold,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              AppText.titleLarge(
                bmi.toStringAsFixed(2),
                fontWeight: FontWeight.bold,
                color: context.primaryColor,
              ),
              const SizedBox(width: 5),
              AppText.labelLarge(
                LocaleKeys.progress_your_current_weight_is.tr(),
                fontWeight: FontWeight.bold,
                color: context.onSecondary,
              ),
              const SizedBox(width: 10),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: const Color(0xff27AE60),
                ),
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 10, vertical: 5),
                child: Text(
                  getBmiCategory(bmi),
                  style: context.textTheme.labelLarge!.copyWith(
                    color: context.onPrimaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 20,
            child: LayoutBuilder(builder: (context, constraints) {
              double barWidth = constraints.maxWidth;
              double bmiPercent = (getCustomBmiPercent(bmi) / 100).clamp(0.0, 1.0);
              double pointerPosition = bmiPercent * barWidth;
              return Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    height: 8,
                    width: context.screenWidth,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      gradient: const LinearGradient(
                        colors: [
                          Color(0xffE74C3C),
                          Color(0xffF1C40F),
                          Color(0xff27AE60),
                          Color(0xff4A90E2),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    right: context.locale.languageCode != 'ar' ? null : pointerPosition, // Center the line
                    left: context.locale.languageCode == 'ar' ? null : pointerPosition, // Center the line
                    child: Container(
                      width: 2,
                      height: 20,
                      color: Colors.black,
                    ),
                  ),
                ],
              );
            }),
          ),
          const SizedBox(height: 15),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(
              4,
              (i) => Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: colors[i],
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    title[i],
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
