
import 'package:equatable/equatable.dart';
import 'package:dio/dio.dart';

class ExerciseSaveAiModel extends Equatable {
  final String description;

  const ExerciseSaveAiModel({
    required this.description,
  });

  /// Convert to form data for API submission
  FormData toFormData() {
    return FormData.fromMap({
      "description": description,
    });
  }

  /// Convert to JSON for local storage or debugging
  Map<String, dynamic> toJson() {
    return {
      "description": description,
    };
  }

  @override
  List<Object?> get props => [description];
}


