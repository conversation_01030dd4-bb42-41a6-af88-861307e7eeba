import 'package:cal/common/consts/typedef.dart';
import 'package:cal/core/network/exceptions.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';

import 'package:cal/features/quick_actions/scan_food/domain/usecases/recognize_food_usecase.dart';
import 'package:cal/features/quick_actions/scan_food/domain/usecases/scan_barcode_use_case.dart';
import 'package:dartz/dartz.dart';

abstract class ScanFoodRepository {
  Future<Either<Failure, FoodModel>> recognizeFood({required AnalyzeFoodParams analyzeFoodParams});
  DataResponse<FoodModel> scanBarcode(ScanBarcodeParams params);
}
