import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';

class FoodDatabaseCard extends StatefulWidget {
  const FoodDatabaseCard({
    super.key,
    this.image,
    this.onTap,
    this.onAddTap,
    this.onDelete,
    this.fats,
    this.carbs,
    this.protein,
    this.description,
    this.isFavorite = false,
    required this.title,
    this.cals,
    this.time,
  });

  final String? image;
  final String title;
  final String? cals;
  final String? fats;
  final String? carbs;
  final String? protein;
  final String? description;

  final String? time;
  final bool isFavorite;
  final void Function()? onTap;
  final void Function()? onAddTap;
  final void Function()? onDelete;

  @override
  State<FoodDatabaseCard> createState() => _FoodDatabaseCardState();
}

class _FoodDatabaseCardState extends State<FoodDatabaseCard> {
  @override
  Widget build(BuildContext context) {
    final cardContent = Container(
      width: context.screenWidth,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: Colors.grey.shade100,
        boxShadow: [
          BoxShadow(
            color: context.onSecondary.withAlpha(35),
            offset: const Offset(-2, 3),
            blurRadius: 10,
          ),
        ],
      ),
      child: Row(
        children: [
          if (widget.image != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: const AppImage.asset(
                Assets.images3in1,
                fit: BoxFit.cover,
                size: 80,
              ),
            ),
          Expanded(
            child: Padding(
              padding: const EdgeInsetsDirectional.symmetric(horizontal: 21, vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppText.labelLarge(
                          widget.title,
                          textAlign: TextAlign.start,
                          color: widget.isFavorite ? context.onSecondary : context.primaryColor,
                          fontWeight: FontWeight.w900,
                        ),
                        buildFirstSubTitle(),
                        const SizedBox(height: 4),
                        buildSecondSubTitle(),
                      ],
                    ),
                  ),
                  AppGestureDetector(
                    onTap: widget.onAddTap,
                    child: Container(
                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(100), color: context.primaryColor),
                      child: Padding(
                        padding: const EdgeInsets.all(9.0),
                        child: Icon(Icons.add, color: context.onPrimaryColor, size: 25),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );

    // Wrap with Slidable if onDelete is provided
    if (widget.onDelete != null) {
      return Slidable(
        key: ValueKey(widget.title),
        endActionPane: ActionPane(
          motion: const DrawerMotion(),
          extentRatio: 0.25,
          children: [
            SlidableAction(
              onPressed: (_) => widget.onDelete!(),
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              icon: Icons.delete,
              label: LocaleKeys.home_delete.tr(),
              borderRadius: BorderRadius.circular(20),
            ),
          ],
        ),
        child: cardContent,
      );
    }

    return cardContent;
  }

  Widget buildFirstSubTitle() {
    return widget.time != null
        ? Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: context.background,
            ),
            padding: const EdgeInsetsDirectional.all(5),
            child: AppText.labelSmall(
              widget.time!,
              color: context.onSecondary,
              fontWeight: FontWeight.w500,
            ),
          )
        : Row(
            children: [
              const AppImage.asset(Assets.imagesCals),
              const SizedBox(width: 5),
              AppText.bodySmall(
                widget.cals!,
                color: context.onSecondary,
                fontWeight: FontWeight.w500,
                style: context.textTheme.bodySmall!.copyWith(fontSize: 14),
              ),
              const SizedBox(width: 5),
              AppText.bodySmall(
                'سعرة حرارية . بالحصة',
                color: context.onSecondary,
                fontWeight: FontWeight.w500,
                style: context.textTheme.bodySmall!.copyWith(fontSize: 14),
              ),
            ],
          );
  }

  Widget buildSecondSubTitle() {
    if (widget.description != null) {
      return AppText.bodySmall(
        widget.description!,
        color: context.onSecondary,
        fontWeight: FontWeight.w500,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        textAlign: TextAlign.start,
        style: const TextStyle(fontSize: 14),
      );
    }
    if (widget.protein != null && widget.carbs != null && widget.fats != null) {
      return Row(
        children: [
          const AppImage.asset(Assets.imagesProtien),
          const SizedBox(width: 5),
          AppText.labelMedium(
            '${widget.protein} غ',
            color: context.onSecondary,
            fontWeight: FontWeight.bold,
          ),
          const SizedBox(
            width: 20,
          ),
          const AppImage.asset(Assets.imagesCarbs),
          const SizedBox(width: 5),
          AppText.labelMedium(
            '${widget.carbs} غ',
            color: context.onSecondary,
            fontWeight: FontWeight.bold,
          ),
          const SizedBox(
            width: 20,
          ),
          const AppImage.asset(Assets.imagesFats),
          const SizedBox(width: 5),
          AppText.labelMedium(
            '${widget.fats} غ',
            color: context.onSecondary,
            fontWeight: FontWeight.bold,
          ),
        ],
      );
    }
    return const SizedBox.shrink();
  }
}
