


import 'dart:developer';

import 'package:shared_preferences/shared_preferences.dart';

class ShPH {
  static SharedPreferences? sharedPreferences;

  static init() async => sharedPreferences = await SharedPreferences.getInstance();

  static dynamic getData({required String key}) => sharedPreferences?.get(key);

  static Future<dynamic> saveData({required String key, required dynamic value}) async {
    log('Saving data - Key: $key, Value: $value');

    if (value is String) return await sharedPreferences?.setString(key, value);
    if (value is int) return await sharedPreferences?.setInt(key, value);
    if (value is bool) return await sharedPreferences?.setBool(key, value);
    if (value is double) return await sharedPreferences?.setDouble(key, value);
  }

  static T? getEnumFromPrefs<T>(String key, List<T> values) {
    final index = getData(key: key) as int?;
    if (index != null && index >= 0 && index < values.length) {
      return values[index];
    }
    return null;
  }

  static Future<bool?> removeData({required String key}) async {
    return await sharedPreferences?.remove(key);
  }

  static Future<bool?> clearData() async {
    return await sharedPreferences?.clear();
  }
}


