import 'dart:developer';

import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:throttling/throttling.dart';

class TimeSelector extends StatefulWidget {
  final int initialHour; // 1 - 12
  final int initialMinute; // 0 - 59
  final bool initialIsAm; // true = AM, false = PM

  final ValueChanged<int> onHourChanged;
  final ValueChanged<int> onMinuteChanged;
  final ValueChanged<bool> onAmPmChanged;

  const TimeSelector({
    super.key,
    required this.initialHour,
    required this.initialMinute,
    required this.initialIsAm,
    required this.onHourChanged,
    required this.onMinuteChanged,
    required this.onAmPmChanged,
  });

  @override
  State<TimeSelector> createState() => _TimeSelectorState();
}

class _TimeSelectorState extends State<TimeSelector> {
  late int selectedHour;
  late int selectedMinute;
  late bool isAm;

  late FixedExtentScrollController hourController;
  late FixedExtentScrollController minuteController;
  late FixedExtentScrollController ampmController;

  late Throttling hourThrottler;
  late Throttling minuteThrottler;
  late Throttling ampmThrottler;

  final List<String> hours = List.generate(12, (i) => '${i + 1}');
  final List<String> minutes = List.generate(60, (i) => i.toString().padLeft(2, '0'));
  final List<String> amPm = ['AM', 'PM'];

  @override
  void initState() {
    super.initState();
    selectedHour = widget.initialHour;
    selectedMinute = widget.initialMinute;
    isAm = widget.initialIsAm;

    hourController = FixedExtentScrollController(initialItem: selectedHour - 1);
    minuteController = FixedExtentScrollController(initialItem: selectedMinute);
    ampmController = FixedExtentScrollController(initialItem: isAm ? 0 : 1);

    hourThrottler = Throttling(duration: const Duration(milliseconds: 300));
    minuteThrottler = Throttling(duration: const Duration(milliseconds: 300));
    ampmThrottler = Throttling(duration: const Duration(milliseconds: 300));
  }

  @override
  void dispose() {
    hourController.dispose();
    minuteController.dispose();
    ampmController.dispose();

    hourThrottler.close();
    minuteThrottler.close();
    ampmThrottler.close();
    super.dispose();
  }

  Widget buildWheel({
    required List<String> values,
    required FixedExtentScrollController controller,
    required ValueChanged<int> onSelectedItemChanged,
    required int selectedIndex,
    required Throttling throttler,
  }) {
    return Expanded(
      child: ListWheelScrollView.useDelegate(
        itemExtent: 40,
        diameterRatio: 1.5,
        physics: const FixedExtentScrollPhysics(),
        onSelectedItemChanged: (index) {
          // HapticFeedbackUtil.mediumImpact();
          setState(() {});
          log(index.toString());
          throttler.throttle(() => onSelectedItemChanged(index));
        },
        controller: controller,
        childDelegate: ListWheelChildBuilderDelegate(
          childCount: values.length,
          builder: (context, index) {
            final isSelected = index == selectedIndex;
            return Center(
              child: Text(
                values[index],
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? Theme.of(context).primaryColor : Colors.black,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              const Expanded(child: Center(child: Text(' ', style: TextStyle(fontWeight: FontWeight.bold)))),
              Expanded(child: Center(child: Text(LocaleKeys.common_minute.tr(), style: const TextStyle(fontWeight: FontWeight.bold)))),
              Expanded(child: Center(child: Text(LocaleKeys.common_hour.tr(), style: const TextStyle(fontWeight: FontWeight.bold)))),
            ],
          ),
        ),
        SizedBox(
          height: 120,
          child: Row(
            children: [
              buildWheel(
                values: amPm,
                controller: ampmController,
                selectedIndex: isAm ? 0 : 1,
                throttler: ampmThrottler,
                onSelectedItemChanged: (index) {
                  isAm = index == 0;
                  widget.onAmPmChanged(isAm);
                },
              ),
              buildWheel(
                values: minutes,
                controller: minuteController,
                selectedIndex: selectedMinute,
                throttler: minuteThrottler,
                onSelectedItemChanged: (index) {
                  selectedMinute = index;
                  widget.onMinuteChanged(selectedMinute);
                },
              ),
              buildWheel(
                values: hours,
                controller: hourController,
                selectedIndex: selectedHour - 1,
                throttler: hourThrottler,
                onSelectedItemChanged: (index) {
                  selectedHour = index + 1;
                  widget.onHourChanged(selectedHour);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
