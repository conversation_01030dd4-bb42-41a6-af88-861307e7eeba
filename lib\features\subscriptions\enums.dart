enum SubscriptionPlanType {
  monthly('monthly'),
  quarterly('quarterly'),
  yearly('yearly');

  final String value;
  const SubscriptionPlanType(this.value);

  static SubscriptionPlanType? fromValue(String? value) {
    if (value == null) return null;
    return SubscriptionPlanType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => SubscriptionPlanType.monthly,
    );
  }
}

enum SubscriptionPurchaseStatus {
  initial('initial'),
  loading('loading'),
  purchased('purchased'),
  error('error');

  final String value;
  const SubscriptionPurchaseStatus(this.value);

  static SubscriptionPurchaseStatus? fromValue(String? value) {
    if (value == null) return null;
    return SubscriptionPurchaseStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => SubscriptionPurchaseStatus.initial,
    );
  }
}
