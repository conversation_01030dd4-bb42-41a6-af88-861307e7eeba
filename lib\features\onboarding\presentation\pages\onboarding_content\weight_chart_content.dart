import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class WeightChartContent extends StatelessWidget {
  const WeightChartContent({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_orange_ai_achieves_long_term_results.tr(),
          contentWidgets: [
            SizedBox(height: context.screenHeight * 0.03),
            _buildWeightChart(context),
          ],
        );
      },
    );
  }

  Widget _buildWeightChart(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.onSecondary.withAlpha(20),
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.symmetric(vertical: 20),
      margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            height: context.screenHeight * .3,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                Expanded(
                  child: WeightChart(
                    userLineColor: context.primaryColor,
                    averageLineColor: Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "${LocaleKeys.onboarding_month.tr()} 6",
                      style: context.textTheme.bodyLarge,
                    ),
                    Text(
                      "${LocaleKeys.onboarding_month.tr()} 1",
                      style: context.textTheme.bodyLarge,
                    ),
                  ],
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 35.0, vertical: 30),
            child: Text(
              LocaleKeys.onboarding_users_maintain_weight_loss_even_after_6_months.tr(),
              style: context.textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500, color: context.onSecondary),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

class WeightChart extends StatelessWidget {
  final Color userLineColor;
  final Color averageLineColor;

  const WeightChart({
    super.key,
    required this.userLineColor,
    required this.averageLineColor,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: SfCartesianChart(
        margin: EdgeInsets.zero,
        plotAreaBorderWidth: 0,
        backgroundColor: Colors.transparent,
        title: ChartTitle(
          text: "${LocaleKeys.onboarding_your_weight.tr()} ",
          textStyle: context.textTheme.bodyLarge!,
          alignment: ChartAlignment.far,
        ),
        primaryXAxis: const CategoryAxis(
          arrangeByIndex: true,
          majorGridLines: MajorGridLines(width: 0),
          axisLine: AxisLine(width: 0),
          majorTickLines: MajorTickLines(size: 0),
          labelStyle: TextStyle(color: Colors.transparent),
          labelPlacement: LabelPlacement.onTicks,
          tickPosition: TickPosition.inside,
          labelsExtent: 0,
          labelAlignment: LabelAlignment.start,
          labelPosition: ChartDataLabelPosition.inside,
          plotOffset: 0,
          interval: 1,
        ),
        primaryYAxis: const NumericAxis(
          majorGridLines: MajorGridLines(width: 0),
          axisLine: AxisLine(width: 0),
          majorTickLines: MajorTickLines(size: 0),
          labelStyle: TextStyle(color: Colors.transparent),
          tickPosition: TickPosition.inside,
          labelsExtent: 0,
          labelAlignment: LabelAlignment.start,
          labelPosition: ChartDataLabelPosition.inside,
          plotOffset: 0,
          interval: 1,
        ),
        tooltipBehavior: TooltipBehavior(enable: true),
        series: <CartesianSeries<WeightData, String>>[
          // Pumped-up user line
          SplineSeries<WeightData, String>(
            name: "الوزن",
            dataSource: generateUserWeightData(),
            xValueMapper: (WeightData data, _) => data.month,
            yValueMapper: (WeightData data, _) => data.weight + 4,
            color: userLineColor,
            width: 4,
            isVisibleInLegend: false,
            markerSettings: const MarkerSettings(isVisible: false),
            enableTooltip: false,
            animationDuration: 1000,
            splineType: SplineType.natural,
            opacity: 0.8,
          ),
          // Average line
          SplineSeries<WeightData, String>(
            name: "المتوسط",
            dataSource: generateAverageWeightData(),
            xValueMapper: (WeightData data, _) => data.month,
            yValueMapper: (WeightData data, _) => data.weight,
            color: averageLineColor,
            width: 4,
            enableTooltip: false,
            animationDuration: 1000,
            splineType: SplineType.natural,
          ),
        ],
      ),
    );
  }

  List<WeightData> generateUserWeightData() {
    return [
      WeightData('M1', 3.00),
      WeightData('M2', 2.80),
      WeightData('M3', 1),
      WeightData('M4', -2.70),
      WeightData('M5', -4.50),
      WeightData('M6', 0.20),
      WeightData('M7', 1.55),
      WeightData('M8', 1.85),
    ];
  }

  List<WeightData> generateAverageWeightData() {
    return [
      WeightData('M1', -2.0),
      WeightData('M2', -1.5),
      WeightData('M3', -1.3),
      WeightData('M4', 1.0),
      WeightData('M5', 4.5),
      WeightData('M6', 5.5),
      WeightData('M7', 5.75),
      WeightData('M8', 5.9),
    ];
  }
}

class WeightData {
  final String month;
  final double weight;

  WeightData(this.month, this.weight);
}
