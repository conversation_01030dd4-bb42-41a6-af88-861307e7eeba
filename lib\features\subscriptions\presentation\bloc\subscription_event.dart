part of 'subscription_bloc.dart';

abstract class SubscriptionEvent extends Equatable {
  const SubscriptionEvent();

  @override
  List<Object?> get props => [];
}

class InitializeSubscriptions extends SubscriptionEvent {
  const InitializeSubscriptions();
}

class LoadSubscriptionPlans extends SubscriptionEvent {
  const LoadSubscriptionPlans();
}

class SelectSubscriptionPlan extends SubscriptionEvent {
  final SubscriptionPlanEntity plan;

  const SelectSubscriptionPlan({required this.plan});

  @override
  List<Object?> get props => [plan];
}

class PurchaseSubscription extends SubscriptionEvent {
  const PurchaseSubscription();
}

class UpdatePurchaseStatus extends SubscriptionEvent {
  final SubscriptionPurchaseStatus status;
  final String? errorMessage;

  const UpdatePurchaseStatus({
    required this.status,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [status, errorMessage];
}

class RestorePurchases extends SubscriptionEvent {
  const RestorePurchases();
}

class VerifyPurchase extends SubscriptionEvent {
  final iap.PurchaseDetails purchaseDetails;

  const VerifyPurchase({required this.purchaseDetails});

  @override
  List<Object?> get props => [purchaseDetails];
}

class HandlePurchaseUpdate extends SubscriptionEvent {
  final iap.PurchaseDetails purchaseDetails;

  const HandlePurchaseUpdate({required this.purchaseDetails});

  @override
  List<Object?> get props => [purchaseDetails];
}
