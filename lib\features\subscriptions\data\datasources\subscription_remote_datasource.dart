import 'dart:async';

import 'package:cal/features/subscriptions/data/models/subscription_plan_model.dart';
import 'package:cal/features/subscriptions/domain/entities/subscription_plan_entity.dart';
import 'package:cal/features/subscriptions/enums.dart';
import 'package:dartz/dartz.dart';
import 'package:in_app_purchase/in_app_purchase.dart' as iap;

abstract class SubscriptionRemoteDataSource {
  /// Check if the store is available
  Future<Either<String, bool>> isStoreAvailable();
  
  /// Get subscription plans from the store
  Future<Either<String, List<SubscriptionPlanModel>>> getSubscriptionPlans();
  
  /// Purchase a subscription
  Future<Either<String, void>> purchaseSubscription(SubscriptionPlanEntity plan);
  
  /// Restore previously purchased subscriptions
  Future<Either<String, void>> restorePurchases();
  
  /// Complete a purchase
  Future<Either<String, void>> completePurchase(iap.PurchaseDetails purchaseDetails);
  
  /// Get purchase updates stream
  Stream<iap.PurchaseDetails> get purchaseUpdates;
}

class SubscriptionRemoteDataSourceImpl implements SubscriptionRemoteDataSource {
  final iap.InAppPurchase _inAppPurchase = iap.InAppPurchase.instance;
  StreamController<iap.PurchaseDetails>? _purchaseController;
  StreamSubscription<List<iap.PurchaseDetails>>? _purchaseSubscription;
  
  SubscriptionRemoteDataSourceImpl() {
    _purchaseController = StreamController<iap.PurchaseDetails>.broadcast();
    _setupPurchaseStream();
  }
  
  void _setupPurchaseStream() {
    _purchaseSubscription = _inAppPurchase.purchaseStream.listen(
      (List<iap.PurchaseDetails> purchaseDetailsList) {
        for (final iap.PurchaseDetails purchaseDetails in purchaseDetailsList) {
          _purchaseController?.add(purchaseDetails);
        }
      },
      onDone: () {
        _purchaseSubscription?.cancel();
      },
      onError: (error) {
        // Handle error
      },
    );
  }
  
  @override
  Future<Either<String, bool>> isStoreAvailable() async {
    try {
      final bool available = await _inAppPurchase.isAvailable();
      return Right(available);
    } catch (e) {
      return Left('Failed to check store availability: $e');
    }
  }
  
  @override
  Future<Either<String, List<SubscriptionPlanModel>>> getSubscriptionPlans() async {
    try {
      final Set<String> productIds = {'monthly_sub', 'quarterly_sub', 'yearly_sub'};
      
      final iap.ProductDetailsResponse response = 
          await _inAppPurchase.queryProductDetails(productIds);
      
      if (response.error != null) {
        return Left('Failed to load subscription plans: ${response.error}');
      }

      if (response.productDetails.isEmpty) {
        // No subscription plans available from the store
        return const Left('No subscription plans available. Please try again later.');
      }
      
      // Map store products to subscription plans
      final List<SubscriptionPlanModel> plans = response.productDetails.map((iap.ProductDetails product) {
        SubscriptionPlanType type;
        List<String> features = [];
        
        if (product.id == 'monthly_sub') {
          type = SubscriptionPlanType.monthly;
          features = ['Personalized workout plans', 'Nutrition tracking', 'Progress analytics'];
        } else if (product.id == 'quarterly_sub') {
          type = SubscriptionPlanType.quarterly;
          features = ['Personalized workout plans', 'Nutrition tracking', 'Progress analytics', 'Premium workout videos'];
        } else {
          type = SubscriptionPlanType.yearly;
          features = ['Personalized workout plans', 'Nutrition tracking', 'Progress analytics', 'Premium workout videos', 'Priority support'];
        }
        
        return SubscriptionPlanModel.fromProductDetails(product, type, features);
      }).toList();
      
      return Right(plans);
    } catch (e) {
      return Left('Failed to load subscription plans: $e');
    }
  }
  
  @override
  Future<Either<String, void>> purchaseSubscription(SubscriptionPlanEntity plan) async {
    try {
      final iap.PurchaseParam purchaseParam = iap.PurchaseParam(
        productDetails: iap.ProductDetails(
          id: plan.id,
          title: plan.title,
          description: plan.description,
          price: plan.price.toString(),
          rawPrice: plan.price,
          currencyCode: 'USD',
        ),
      );

      // For subscriptions, use buyNonConsumable for one-time purchases
      await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      return const Right(null);
    } catch (e) {
      return Left('Failed to initiate purchase: $e');
    }
  }
  
  @override
  Future<Either<String, void>> restorePurchases() async {
    try {
      await _inAppPurchase.restorePurchases();
      return const Right(null);
    } catch (e) {
      return Left('Failed to restore purchases: $e');
    }
  }
  
  @override
  Future<Either<String, void>> completePurchase(iap.PurchaseDetails purchaseDetails) async {
    try {
      await _inAppPurchase.completePurchase(purchaseDetails);
      return const Right(null);
    } catch (e) {
      return Left('Failed to complete purchase: $e');
    }
  }
  
  @override
  Stream<iap.PurchaseDetails> get purchaseUpdates => _purchaseController!.stream;
  
  void dispose() {
    _purchaseSubscription?.cancel();
    _purchaseController?.close();
  }
}
