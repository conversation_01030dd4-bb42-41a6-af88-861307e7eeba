// common_remote_food_model.dart

CommonRemoteFoodModel commonRemoteFoodModelFromJson(str) => CommonRemoteFoodModel.fromJson(str);
// common_remote_food_model.dart

class CommonRemoteFoodModel {
  String? message;
  CommonRemoteFoodData? data;

  CommonRemoteFoodModel({this.message, this.data});

  factory CommonRemoteFoodModel.fromJson(Map<String, dynamic> json) => CommonRemoteFoodModel(
        message: json["message"],
        data: json["data"] == null ? null : CommonRemoteFoodData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data?.toJson(),
      };
}

class CommonRemoteFoodData {
  int? id;
  int? userId;
  String? name;
  String? englishName;
  String? arabicName;
  int? calories;
  double? fats;
  double? carbs;
  double? protein;
  String? ingredients;
  String? serving;
  int? healthScore;
  bool? halal;
  bool? saved;
  String? createdAt;
  String? updatedAt;

  CommonRemoteFoodData({
    this.id,
    this.userId,
    this.name,
    this.englishName,
    this.arabicName,
    this.calories,
    this.fats,
    this.carbs,
    this.protein,
    this.ingredients,
    this.serving,
    this.healthScore,
    this.halal,
    this.saved,
    this.createdAt,
    this.updatedAt,
  });

  factory CommonRemoteFoodData.fromJson(Map<String, dynamic> json) => CommonRemoteFoodData(
        id: json["id"],
        userId: json["user_id"],
        name: json["name"],
        englishName: json["english_name"],
        arabicName: json["arabic_name"],
        calories: json["calories"],
        fats: json["fats"],
        carbs: json["carbs"],
        protein: json["protein"],
        ingredients: json["ingredients"],
        serving: json["serving"],
        healthScore: json["health_score"],
        halal: json["halal"],
        saved: json["saved"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "name": name,
        "english_name": englishName,
        "arabic_name": arabicName,
        "calories": calories,
        "fats": fats,
        "carbs": carbs,
        "protein": protein,
        "ingredients": ingredients,
        "serving": serving,
        "health_score": healthScore,
        "halal": halal,
        "saved": saved,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}
