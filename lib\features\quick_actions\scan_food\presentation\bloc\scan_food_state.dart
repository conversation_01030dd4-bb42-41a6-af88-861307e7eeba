part of 'scan_food_bloc.dart';

enum ScanFoodStatus { initial, loading, processing, success, error }

enum OriginalScanType { foodRecognition, barcode }

class ScanFoodState extends Equatable {
  final ScanFoodStatus status;
  final File? capturedImage;
  final String? errorMessage;
  final FoodModel? recognizedFood;
  final FoodModel? scanBarcode;
  final bool isRetry;
  final OriginalScanType? originalScanType;
  final String? originalBarcode;
  final bool? originalIsLabel;

  const ScanFoodState({
    this.status = ScanFoodStatus.initial,
    this.capturedImage,
    this.errorMessage,
    this.recognizedFood,
    this.isRetry = false,
    this.scanBarcode,
    this.originalScanType,
    this.originalBarcode,
    this.originalIsLabel,
  });

  ScanFoodState copyWith({
    ScanFoodStatus? status,
    File? capturedImage,
    String? errorMessage,
    FoodModel? recognizedFood,
    bool? isRetry,
    FoodModel? scanBarcode,
    OriginalScanType? originalScanType,
    String? originalBarcode,
    bool? originalIsLabel,
  }) {
    return ScanFoodState(
      scanBarcode: scanBarcode ?? this.scanBarcode,
      status: status ?? this.status,
      capturedImage: capturedImage ?? this.capturedImage,
      errorMessage: errorMessage,
      recognizedFood: recognizedFood,
      isRetry: isRetry ?? this.isRetry,
      originalScanType: originalScanType ?? this.originalScanType,
      originalBarcode: originalBarcode ?? this.originalBarcode,
      originalIsLabel: originalIsLabel ?? this.originalIsLabel,
    );
  }

  @override
  List<Object?> get props => [
        status,
        capturedImage,
        errorMessage,
        recognizedFood,
        isRetry,
        scanBarcode,
        originalScanType,
        originalBarcode,
        originalIsLabel,
      ];
}
