import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/core/local_models/user_model/user_model.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:isar/isar.dart';

import '../../../../common/widgets/app_text.dart';
import '../../../../common/widgets/large_button.dart';
import '../../../../core/datasources/user_local_data_source.dart';
import '../../../../core/di/injection.dart';
import '../../../onboarding/enums.dart';
import '../../../onboarding/presentation/widgets/onboarding_option.dart';

class ChangeGenderScreen extends StatefulWidget {
  const ChangeGenderScreen({super.key, required this.user});

  final UserModel user;

  @override
  State<ChangeGenderScreen> createState() => _ChangeGenderScreenState();
}

class _ChangeGenderScreenState extends State<ChangeGenderScreen> {
  String selectedGender = '';

  @override
  void initState() {
    super.initState();
    selectedGender = widget.user.gender!;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsetsDirectional.symmetric(horizontal: 20),
          child: Column(
            children: [
              const SizedBox(height: 30),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    borderRadius: BorderRadius.circular(50),
                    onTap: () {
                      context.pop();
                    },
                    child: CircleAvatar(
                      radius: 25,
                      backgroundColor: context.onSecondary.withAlpha(21),
                      child: Icon(
                        Icons.arrow_back,
                        color: context.onSecondary,
                        size: 18,
                      ),
                    ),
                  ),
                  AppText.titleSmall('Edit Gender', color: context.onSecondary, fontWeight: FontWeight.w700),
                  const CircleAvatar(
                    radius: 25,
                    backgroundColor: Colors.transparent,
                    child: Icon(
                      Icons.arrow_back,
                      color: Colors.transparent,
                      size: 18,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 100),
              const SizedBox(height: 60),
              ...Gender.values
                  .map(
                    (gender) => OnboardingOption(
                      isSelected: selectedGender == gender.localizedNameKey.tr(),
                      text: gender.localizedName,
                      isTextCentered: true,
                      onSelected: () {
                        setState(() {
                          selectedGender = gender.localizedNameKey.tr();
                        });
                      },
                    ),
                  )
                  .toList(),
              const Spacer(),
              LargeButton(
                onPressed: () async {
                  await UserLocalDataSource(getIt<Isar>()).saveUserData(widget.user.copyWith(gender: selectedGender)).then((val) async {
                    if (context.mounted) {
                      context.pop(true);
                    }
                  });
                },
                text: 'Save Changes',
                backgroundColor: context.primaryColor,
                circularRadius: 16,
                textStyle: context.textTheme.bodyMedium!.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
