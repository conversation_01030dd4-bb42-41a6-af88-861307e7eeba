import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/core/local_models/daily_data_model/daily_data_model.dart';
import 'package:cal/core/local_models/weigh_history_model/weight_history_model.dart';
import 'package:cal/core/local_models/weigh_history_model/wieght_history_service.dart';
import 'package:flutter/material.dart';
import 'package:wheel_slider/wheel_slider.dart';

import '../../../../common/widgets/large_button.dart';
import '../../../../core/local_models/daily_data_model/daily_user_info_service.dart';

class ProgressWeightScaleScreen extends StatefulWidget {
  const ProgressWeightScaleScreen({super.key, required this.model});

  final DailyUserDataModel model;

  @override
  State<ProgressWeightScaleScreen> createState() => _ProgressWeightScaleScreenState();
}

class _ProgressWeightScaleScreenState extends State<ProgressWeightScaleScreen> {
  double newWeight = 0;

  @override
  void initState() {
    newWeight = widget.model.weight;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                          borderRadius: BorderRadius.circular(50),
                          onTap: () {
                            context.pop(false);
                          },
                          child: CircleAvatar(
                            radius: 25,
                            backgroundColor: context.onSecondary.withAlpha(21),
                            child: Icon(
                              Icons.arrow_back,
                              color: context.onSecondary,
                              size: 18,
                            ),
                          ),
                        ),
                        AppText.titleSmall('Edit weight', color: context.onSecondary, fontWeight: FontWeight.w700),
                        const CircleAvatar(
                          radius: 25,
                          backgroundColor: Colors.transparent,
                          child: Icon(
                            Icons.arrow_back,
                            color: Colors.transparent,
                            size: 18,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: context.screenHeight * .3),
                    Text(
                      'Current weight',
                      style: context.textTheme.titleSmall!.copyWith(color: context.onSecondary, fontWeight: FontWeight.w700),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    Text(
                      '${newWeight.toStringAsFixed(1)}Kg',
                      style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      height: 80,
                      child: WheelSlider(
                        isInfinite: false,
                        totalCount: 8000,
                        isVibrate: false,

                        // Represents 0.0 to 800.0 kg in 0.1 kg steps
                        initValue: (widget.model.weight * 10).toInt(),
                        // starting index based on initial weight
                        onValueChanged: (index) {
                          final double currentWeight = index / 10; // convert index back to kg (decimal)
                          setState(() => newWeight = currentWeight); // update UI with new weight
                        },
                        itemSize: 20,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.symmetric(horizontal: 20),
              child: LargeButton(
                onPressed: () async {
                  await WeightHistoryService.saveWeightHistory(
                      WeightHistoryModel(weight: newWeight, date: DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day)));
                  await DailyUserInfoService.saveDailyData(widget.model.copyWith(
                    weight: newWeight,
                    bmi: DailyUserInfoService.calculateBMI(newWeight, widget.model.height),
                  )).then((val) {
                    if (context.mounted) {
                      context.pop(true);
                    }
                  });
                },
                text: 'Save Changes',
                backgroundColor: context.primaryColor,
                circularRadius: 16,
                textStyle: context.textTheme.bodyMedium!.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
