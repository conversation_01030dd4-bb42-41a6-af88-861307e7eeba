import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_option.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class WhatYouWantToAchieveContent extends StatefulWidget {
  const WhatYouWantToAchieveContent({super.key});

  @override
  State<WhatYouWantToAchieveContent> createState() => _WhatYouWantToAchieveContentState();
}

class _WhatYouWantToAchieveContentState extends State<WhatYouWantToAchieveContent> {
  @override
  void initState() {
    super.initState();
    context.read<OnboardingBloc>().add(RegisterScreenValidation((state) => state.achieve != null));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_waht_you_want_to_achieve.tr(),
          centerContent: true,
          contentWidgets: [
            ...Achieve.values.map(
              (achieve) => OnboardingOption(
                isSelected: state.achieve == achieve,
                imagePath: achieve.icon,
                text: achieve.localizedName,
                onSelected: () => context.read<OnboardingBloc>().add(UpdateWhatYouWantToAchieve(achieve)),
              ),
            ),
          ],
        );
      },
    );
  }
}
