import 'package:flutter/material.dart';

import '../../domain/entities/user_entity.dart';
import '../../domain/entities/auth_token_entity.dart';
import '../../domain/repositories/authentication_repository.dart';
import '../datasources/authentication_local_datasource.dart';
import '../datasources/authentication_remote_datasource.dart';
import '../models/user_model.dart';

// Implementation of Authentication Repository
class AuthenticationRepositoryImpl implements AuthenticationRepository {
  final AuthenticationRemoteDataSource _remoteDataSource;
  final AuthenticationLocalDataSource _localDataSource;

  AuthenticationRepositoryImpl({
    required AuthenticationRemoteDataSource remoteDataSource,
    required AuthenticationLocalDataSource localDataSource,
  }) : _remoteDataSource = remoteDataSource,
       _localDataSource = localDataSource;

  @override
  Future<UserEntity> signInWithApple({required BuildContext context}) async {
    try {
      // Get token from remote data source
      final token = await _remoteDataSource.signInWithApple(context: context);
      
      // Save token locally
      await _localDataSource.saveToken(token);
      
      // Create user entity (you might want to get user info from backend)
      // For now, creating a basic user entity
      final user = UserModel(
        id: 'apple_user_${DateTime.now().millisecondsSinceEpoch}',
        email: '<EMAIL>', // This should come from backend response
        authProvider: AuthProvider.apple,
      );
      
      // Save user locally
      await _localDataSource.saveUser(user);
      
      return user;
    } catch (e) {
      throw Exception('Failed to sign in with Apple: $e');
    }
  }

  @override
  Future<UserEntity> signInWithGoogle() async {
    try {
      // Get token from remote data source
      final token = await _remoteDataSource.signInWithGoogle();
      
      // Save token locally
      await _localDataSource.saveToken(token);
      
      // Create user entity (you might want to get user info from backend)
      // For now, creating a basic user entity
      final user = UserModel(
        id: 'google_user_${DateTime.now().millisecondsSinceEpoch}',
        email: '<EMAIL>', // This should come from backend response
        authProvider: AuthProvider.google,
      );
      
      // Save user locally
      await _localDataSource.saveUser(user);
      
      return user;
    } catch (e) {
      throw Exception('Failed to sign in with Google: $e');
    }
  }

  @override
  Future<void> signOut() async {
    try {
      // Sign out from remote services
      await _remoteDataSource.signOut();
      
      // Clear local data
      await _localDataSource.clearAll();
    } catch (e) {
      throw Exception('Failed to sign out: $e');
    }
  }

  @override
  Future<UserEntity?> getCurrentUser() async {
    try {
      return await _localDataSource.getUser();
    } catch (e) {
      return null;
    }
  }

  @override
  Future<AuthTokenEntity?> getCurrentToken() async {
    try {
      return await _localDataSource.getToken();
    } catch (e) {
      return null;
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      final token = await getCurrentToken();
      if (token == null) return false;
      
      // Check if token is expired
      return !token.isExpired;
    } catch (e) {
      return false;
    }
  }
}

