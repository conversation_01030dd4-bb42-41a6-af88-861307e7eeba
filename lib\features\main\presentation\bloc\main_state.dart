part of 'main_bloc.dart';

class MainState {
  List<StreakModel>? streaks;
  BlocStatus status;
  int? streaksNumber;

  MainState({
    this.streaks,
    this.status = BlocStatus.initial,
    this.streaksNumber,
  });

  MainState copyWith({
    List<StreakModel>? streaks,
    BlocStatus? status,
    int? streaksNumber,
  }) {
    return MainState(
      streaks: streaks ?? this.streaks,
      status: status ?? this.status,
      streaksNumber: streaksNumber ?? this.streaksNumber,
    );
  }
}
