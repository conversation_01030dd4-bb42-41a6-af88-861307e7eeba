// import 'package:flutter/material.dart';
// import 'package:health/health.dart';

// class AppleHealthConfig {
//   final Health _health = Health();

//   // List of health data types you want to request permission for
//   late List<HealthDataType> _types;

//   /// Requests permissions for Apple Health data types
//   Future<bool> requestAppleHealthPermissions(BuildContext context) async {
//     if (Theme.of(context).platform != TargetPlatform.iOS) {
//       debugPrint("This function is intended for iOS only.");
//       return false;
//     }
//     await _health.configure();
//     _types = [
//       HealthDataType.STEPS,
//       // HealthDataType.HEART_RATE,
//       // HealthDataType.ACTIVE_ENERGY_BURNED,
//       // HealthDataType.BODY_MASS_INDEX,
//       // HealthDataType.SLEEP_IN_BED,
//     ];

//     // Request permissions for the specified health data types
//     final bool hasPermissions = await _health.requestAuthorization(_types);
//     if (hasPermissions) {
//       debugPrint("Permissions granted for Apple Health.");
//       return true;
//     } else {
//       debugPrint("Failed to get permissions for Apple Health.");
//       return false;
//     }
//   }
// }
