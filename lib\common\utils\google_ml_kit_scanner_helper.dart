import 'dart:developer';
import 'dart:io';
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart';

class BarcodeScannerService {
  final BarcodeScanner _barcodeScanner = BarcodeScanner();

  Future<String?> scanFromFile(File imageFile) async {
    try {
      final inputImage = InputImage.fromFile(imageFile);
      final barcodes = await _barcodeScanner.processImage(inputImage);

      for (final barcode in barcodes) {
        return barcode.rawValue;
      }

      return null;
    } catch (e) {
      log('Error scanning barcode: $e');
      return null;
    }
  }

  void dispose() {
    _barcodeScanner.close();
  }
}
