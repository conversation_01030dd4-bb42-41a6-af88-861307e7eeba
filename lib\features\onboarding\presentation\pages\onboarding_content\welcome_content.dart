import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/gradient_text.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Birth Date Screen
class WelcomeContent extends StatelessWidget {
  const WelcomeContent({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: "",
          contentWidgets: [
            const SizedBox(height: 200),
            Center(
              child: Text.rich(
                textAlign: TextAlign.center,
                TextSpan(
                  children: [
                    TextSpan(
                      text: "${LocaleKeys.onboarding_welcome.tr()} \n ${LocaleKeys.onboarding_welcome_desc.tr()}",
                      style: context.textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w400, height: 2),
                    ),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.baseline,
                      baseline: TextBaseline.alphabetic,
                      child: GradientText(
                        text: ' AI ',
                        style: context.textTheme.titleLarge!.copyWith(fontWeight: FontWeight.bold),
                        gradient: LinearGradient(colors: [context.onSecondary, context.onSecondary]),
                      ),
                    ),
                    TextSpan(
                      text: 'Orange',
                      style: context.textTheme.titleLarge!
                          .copyWith(fontWeight: FontWeight.bold, color: context.primaryColor),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              LocaleKeys.onboarding_welcome_second_desc.tr(),
              style: context.textTheme.titleSmall!.copyWith(fontWeight: FontWeight.w300, color: Colors.grey.shade700),
              textAlign: TextAlign.center,
            ),
          ],
        );
      },
    );
  }
}
