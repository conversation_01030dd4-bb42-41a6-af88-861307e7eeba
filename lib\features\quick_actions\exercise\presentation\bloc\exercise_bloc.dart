import 'package:bloc/bloc.dart';
import 'package:cal/core/local_models/exercise_model/exercise_model.dart';
import 'package:cal/features/home/<USER>/bloc/recent_activity_bloc.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_ai_model.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_model.dart';
import 'package:cal/features/quick_actions/exercise/domain/usecases/save_exercise_ai_usecase.dart';
import 'package:cal/features/quick_actions/exercise/domain/usecases/save_exercise_usecase.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

part 'exercise_event.dart';
part 'exercise_state.dart';

@injectable
class ExerciseBloc extends Bloc<ExerciseEvent, ExerciseState> {
  final SaveExerciseUseCase saveExerciseUseCase;
  final SaveExerciseAiUseCase saveExerciseAiUseCase;
  final RecentActivityBloc _recentActivityBloc;

  ExerciseBloc({
    required this.saveExerciseUseCase,
    required this.saveExerciseAiUseCase,
    required RecentActivityBloc recentActivityBloc,
  })  : _recentActivityBloc = recentActivityBloc,
        super(ExerciseInitial()) {
    on<SaveExercise>(_onSaveExercise);
    on<SaveExerciseAi>(_onSaveExerciseAi);
    on<RetryExercise>(_onRetryExercise);
  }

  Future<void> _onSaveExercise(
    SaveExercise event,
    Emitter<ExerciseState> emit,
  ) async {
    emit(ExerciseLoading());

    // Add loading exercise to RecentActivityBloc
    final loadingExercise = ExerciseModel(
      date: DateTime.now(),
      typeEnglish: event.exercise.typeEnglish,
      typeArabic: event.exercise.typeArabic,
      duration: event.exercise.duration,
      intensity: event.exercise.intensity,
      calories: event.exercise.calories,
      isLoading: true,
      tempId: DateTime.now().millisecondsSinceEpoch.toString(),
    );
    _recentActivityBloc.add(AddExercise(exercise: loadingExercise));

    final result = await saveExerciseUseCase(event.exercise);
    result.fold(
      (failure) {
        emit(ExerciseError(failure));
        // Update RecentActivityBloc with error status
        final errorExercise = loadingExercise.copyWith(
          isLoading: false,
          hasError: true,
        );
        _recentActivityBloc.add(UpdateExercise(errorExercise));
      },
      (savedExercise) {
        emit(ExerciseSaved());
        // Update RecentActivityBloc with saved exercise
        final successExercise = savedExercise.copyWith(
          isLoading: false,
          tempId: loadingExercise.tempId,
        );
        _recentActivityBloc.add(UpdateExercise(successExercise));
      },
    );
  }

  Future<void> _onSaveExerciseAi(
    SaveExerciseAi event,
    Emitter<ExerciseState> emit,
  ) async {
    emit(ExerciseLoading());

    // Add loading exercise to RecentActivityBloc
    final loadingExercise = ExerciseModel(
      date: DateTime.now(),
      description: event.exercise.description,
      source: 'ai',
      isLoading: true,
      tempId: DateTime.now().millisecondsSinceEpoch.toString(),
    );
    _recentActivityBloc.add(AddExercise(exercise: loadingExercise));

    final result = await saveExerciseAiUseCase(event.exercise);
    result.fold(
      (failure) {
        emit(ExerciseError(failure));
        // Update RecentActivityBloc with error status
        final errorExercise = loadingExercise.copyWith(
          isLoading: false,
          hasError: true,
        );
        _recentActivityBloc.add(UpdateExercise(errorExercise));
      },
      (savedExercise) {
        emit(ExerciseSaved());
        // Update RecentActivityBloc with saved exercise
        final successExercise = savedExercise.copyWith(
          isLoading: false,
          tempId: loadingExercise.tempId,
        );
        _recentActivityBloc.add(UpdateExercise(successExercise));
      },
    );
  }

  Future<void> _onRetryExercise(
    RetryExercise event,
    Emitter<ExerciseState> emit,
  ) async {
    // For now, just emit initial state
    // In a full implementation, we would store the original exercise data
    // and retry the last failed operation
    emit(ExerciseInitial());
  }
}
