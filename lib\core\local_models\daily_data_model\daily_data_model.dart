import 'package:equatable/equatable.dart';
import 'package:isar/isar.dart';

part 'daily_data_model.g.dart';

@Collection(inheritance: false)
// ignore: must_be_immutable
class DailyUserDataModel extends Equatable {
  Id id;

  @Index(unique: true)
  final DateTime date;

  final double targetCalories;
  final double targetCarbs;
  final double targetFat;
  final double targetProtein;
  final double consumedCalories;
  final double consumedCarbs;
  final double consumedFat;
  final double consumedProtein;
  final double burnedCalories;
  final double weight;
  final double height;
  final double bmi;

  final double sum;

  DailyUserDataModel({
    this.id = Isar.autoIncrement,
    required this.date,
    required this.targetCalories,
    required this.targetCarbs,
    required this.targetFat,
    required this.targetProtein,
    required this.consumedCalories,
    required this.consumedCarbs,
    required this.consumedFat,
    required this.consumedProtein,
    required this.burnedCalories,
    required this.weight,
    required this.height,
    required this.bmi,
  }) : sum = consumedProtein + consumedCarbs + consumedFat;

  DailyUserDataModel copyWith({
    DateTime? date,
    double? targetCalories,
    double? targetCarbs,
    double? targetFat,
    double? targetProtein,
    double? consumedCalories,
    double? consumedCarbs,
    double? consumedFat,
    double? consumedProtein,
    double? burnedCalories,
    double? weight,
    double? height,
    double? bmi,
  }) {
    final newConsumedProtein = consumedProtein ?? this.consumedProtein;
    final newConsumedCarbs = consumedCarbs ?? this.consumedCarbs;
    final newConsumedFat = consumedFat ?? this.consumedFat;

    return DailyUserDataModel(
      id: id,
      date: date ?? this.date,
      targetCalories: targetCalories ?? this.targetCalories,
      targetCarbs: targetCarbs ?? this.targetCarbs,
      targetFat: targetFat ?? this.targetFat,
      targetProtein: targetProtein ?? this.targetProtein,
      consumedCalories: consumedCalories ?? this.consumedCalories,
      consumedCarbs: newConsumedCarbs,
      consumedFat: newConsumedFat,
      consumedProtein: newConsumedProtein,
      burnedCalories: burnedCalories ?? this.burnedCalories,
      weight: weight ?? this.weight,
      height: height ?? this.height,
      bmi: bmi ?? this.bmi,
    );
  }

  @override
  @ignore
  List<Object?> get props => [
        date,
        targetCalories,
        targetCarbs,
        targetFat,
        targetProtein,
        consumedCalories,
        consumedCarbs,
        consumedFat,
        consumedProtein,
        burnedCalories,
        weight,
        height,
        bmi,
      ];
}
