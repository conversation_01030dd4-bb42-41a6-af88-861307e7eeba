{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/SDK/android-sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/SDK/android-sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/SDK/android-sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/SDK/android-sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-d932aa0a3969574a4f8d.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-209eaca9f9e665a043e9.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-fa283e0ed6ef3bd30bcb.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-209eaca9f9e665a043e9.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-fa283e0ed6ef3bd30bcb.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-d932aa0a3969574a4f8d.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}