import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class WeightGoalVisualizationContent extends StatelessWidget {
  const WeightGoalVisualizationContent({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        final isWeightLoss = state.goal == Goal.weightLoss;
        final goalAmount = (isWeightLoss ? state.weight! - state.targetWeight! : state.targetWeight! - state.weight!)
            .toStringAsFixed(1);
        final goalString = goalAmount;

        return OnboardingScreenTemplate(
          contentWidgets: [
            const SizedBox(height: 80),
            Text.rich(
              textAlign: TextAlign.center,
              TextSpan(
                children: [
                  WidgetSpan(
                    alignment: PlaceholderAlignment.baseline,
                    baseline: TextBaseline.alphabetic,
                    child: Text(
                      isWeightLoss ? LocaleKeys.onboarding_loss.tr() : LocaleKeys.onboarding_gain.tr(),
                      style: context.textTheme.titleMedium!.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  TextSpan(
                    text: " $goalString",
                    style: context.textTheme.titleMedium!
                        .copyWith(fontWeight: FontWeight.w600, color: context.primaryColor),
                  ),
                  TextSpan(
                    text: " ${LocaleKeys.onboarding_kg.tr()} ${LocaleKeys.onboarding_realistic_goal.tr()}",
                    style: context.textTheme.titleMedium!.copyWith(fontWeight: FontWeight.w600),
                  ),
                  TextSpan(
                    text: " ${LocaleKeys.onboarding_onboarding_weight_loss_goal_subtitle.tr()}",
                    style: context.textTheme.titleMedium!.copyWith(fontWeight: FontWeight.w600),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            Text(
              LocaleKeys.onboarding_onboarding_goal_statistics.tr(),
              style: context.textTheme.bodyLarge!.copyWith(
                color: context.onSecondary.withAlpha(178),
              ),
              textAlign: TextAlign.center,
            )
          ],
        );
      },
    );
  }
}
