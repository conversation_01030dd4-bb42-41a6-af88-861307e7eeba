// import 'package:equatable/equatable.dart';

// class FoodModel extends Equatable {
//   final String foodName;
//   final double calories;
//   final double protein;
//   final double carbs;
//   final double fat;
//   final bool isHalal;
//   final List<String> ingredients;
//   final String imageUrl;

//   const FoodModel({
//     required this.foodName,
//     required this.calories,
//     required this.protein,
//     required this.carbs,
//     required this.fat,
//     required this.isHalal,
//     required this.ingredients,
//     required this.imageUrl,
//   });

//   @override
//   List<Object?> get props => [
//     foodName,
//     calories,
//     protein,
//     carbs,
//     fat,
//     isHalal,
//     ingredients,
//     imageUrl,
//   ];
// }