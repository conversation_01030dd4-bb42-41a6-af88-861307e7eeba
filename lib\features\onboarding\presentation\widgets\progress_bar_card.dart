import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/features/onboarding/presentation/widgets/custom_linear_progress_indicator.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:flutter/material.dart';

class ProgressBarCard extends StatelessWidget {
  final double value;
  const ProgressBarCard({super.key, required this.value});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(11),
      decoration: BoxDecoration(
        color: context.onPrimaryColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(11),
            decoration: BoxDecoration(color: context.background, borderRadius: BorderRadius.circular(8)),
            child: const AppImage.asset(Assets.imagesBrokenHeart),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      LocaleKeys.onboarding_health_result.tr(),
                      style: context.textTheme.bodySmall!
                          .copyWith(fontWeight: FontWeight.w200, color: context.onSecondary),
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      "${value.toInt()}/10",
                      style: context.textTheme.bodySmall!
                          .copyWith(fontWeight: FontWeight.w900, color: context.onSecondary),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                CustomLinearProgressIndicator(
                  color: context.primaryContainer,
                  minHeight: 4,
                  value: value / 10,
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
