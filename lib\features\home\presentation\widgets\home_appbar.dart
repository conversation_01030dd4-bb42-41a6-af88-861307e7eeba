import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/core/local_models/streak_model/streak_model.dart';
import 'package:cal/features/home/<USER>/widgets/streak_dialog.dart';
import 'package:cal/features/main/presentation/bloc/main_bloc.dart';
import 'package:cal/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

AppBar homeAppBar(BuildContext context) {
  return AppBar(
    forceMaterialTransparency: true,
    elevation: 0,
    automaticallyImplyLeading: false,
    centerTitle: false,
    title: const AppImage.asset(Assets.imagesOrangeAi, height: 24, width: 24),
    actionsPadding: const EdgeInsets.symmetric(horizontal: 24),
    actions: [
      BlocBuilder<MainBloc, MainState>(
        builder: (context, state) {
          return AppGestureDetector(
            onTap: () => showDialog(
                animationStyle: AnimationStyle(reverseCurve: Curves.easeInCubic, duration: Duration(milliseconds: 200)),
                context: context,
                builder: (_) => StreakDialog(bloc: context.read<MainBloc>())),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20.0),
                shape: BoxShape.rectangle,
                border: Border.all(width: 1, color: Colors.white),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.local_fire_department, color: Colors.orange, size: 16.0),
                  const SizedBox(width: 8),
                  Text(
                    state.streaksNumber.toString(),
                    style: context.textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w800),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    ],
  );
}

int longestConsecutiveDays(List<StreakModel> dates) {
  if (dates.isEmpty) return 0;
  if (dates.length == 1) return 1;

  dates.sort((a, b) => a.streakDate.compareTo(b.streakDate));

  int maxLength = 1;
  int currentLength = 1;

  for (int i = 1; i < dates.length; i++) {
    final prev = dates[i - 1].streakDate;
    final curr = dates[i].streakDate;

    final difference = curr.difference(prev).inDays;

    if (difference == 1) {
      currentLength++;
      maxLength = currentLength > maxLength ? currentLength : maxLength;
    } else if (difference > 1) {
      currentLength = 1;
    }
  }
  return maxLength;
}
