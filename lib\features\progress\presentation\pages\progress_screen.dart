import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/features/progress/presentation/manager/progress_notifier/progress_value_notifier.dart';
import 'package:cal/features/progress/presentation/pages/progress_weight_scale_screen.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../../../common/consts/app_keys.dart';
import '../../../../common/utils/shared_preferences_helper.dart';
import '../../../../core/local_models/daily_data_model/daily_data_model.dart';
import '../../../../core/local_models/daily_data_model/daily_user_info_service.dart';
import '../widgets/bmi_chart.dart';
import '../widgets/cals_sum_chart.dart';
import '../widgets/helpers/progress_helper.dart';
import '../widgets/progress_metric_card.dart';
import '../widgets/progress_tab_bar.dart';
import '../widgets/target_progress_chart.dart';

class ProgressScreen extends StatefulWidget {
  const ProgressScreen({super.key});

  @override
  State<ProgressScreen> createState() => _ProgressScreenState();
}

class _ProgressScreenState extends State<ProgressScreen> {
  final ProgressValueNotifier valueNotifier = ProgressValueNotifier();

  List<DailyUserDataModel> sumCalsDailyData = [];
  List<DailyUserDataModel> targetProgressDailyData = [];
  DailyUserDataModel? model;

  @override
  void initState() {
    fetchDailyDataForWeeks(0);
    fetchDailyDataForMonths(0);
    fetchDailyBmi();
    super.initState();
  }

  fetchDailyBmi() async {
    model = await DailyUserInfoService.getDailyBmi(startDate: DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day));
    setState(() {});
  }

  fetchDailyDataForWeeks(int index) async {
    sumCalsDailyData =
        await DailyUserInfoService.getDailyDataForWeeks(startDate: ProgressHelper.getLastSaturday().add(Duration(days: index * 7)));
    setState(() {});
  }

  fetchDailyDataForMonths(int index) async {
    targetProgressDailyData = await DailyUserInfoService.getDailyDataForMonths(
      days: index != 3
          ? index == 0
              ? 90
              : index == 1
                  ? 180
                  : 365
          : null,
    );
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.background,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsetsDirectional.only(start: 20, end: 20, bottom: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              AppText.titleLarge(
                LocaleKeys.progress_title.tr(),
                color: context.onSecondary,
                fontWeight: FontWeight.bold,
              ),
              const SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: ProgressMetricCard(
                      title: LocaleKeys.progress_scale_time.tr(),
                      description: '3 / 7 ${LocaleKeys.progress_days.tr()}',
                      buttonTitle: LocaleKeys.progress_enter_weight.tr(),
                      progressColor: context.primaryColor,
                      progress: 0.5,
                      icon: Assets.imagesScale,
                      onTap: () async {
                        await context.push(ProgressWeightScaleScreen(model: model!)).then((val){
                          fetchDailyBmi();
                          fetchDailyDataForMonths(0);
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 24),
                  Expanded(
                    child: ProgressMetricCard(
                      title: LocaleKeys.progress_registration_days.tr(),
                      description: '${ShPH.getData(key: AppKeys.progressStreak)} / 7 ${LocaleKeys.progress_days.tr()}',
                      buttonTitle: LocaleKeys.progress_enter_weight.tr(),
                      progressColor: context.primaryColor,
                      progress: ShPH.getData(key: AppKeys.progressStreak) / 7,
                      icon: Assets.imagesApple,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              ValueListenableBuilder(
                valueListenable: valueNotifier.targetProgressIndex,
                builder: (context, index, _) => ProgressTabBar(
                  isFill: true,
                  indexValue: index,
                  onTabChanged: (i) {
                    valueNotifier.changeTargetProgressIndex(i);
                    fetchDailyDataForMonths(i);
                  },
                  titles: [
                    '90 ${LocaleKeys.progress_days.tr()}',
                    '6 ${LocaleKeys.progress_months.tr()}',
                    '1 ${LocaleKeys.progress_year.tr()}',
                    LocaleKeys.progress_all.tr(),
                  ],
                ),
              ),
              const SizedBox(height: 17),
              targetProgressDailyData.isNotEmpty
                  ? ValueListenableBuilder(
                      valueListenable: valueNotifier.targetProgressIndex,
                      builder: (context, index, _) => TargetProgressChart(
                        chartDate: ProgressHelper.getTheData(targetProgressDailyData, index),
                      ),
                    )
                  : const CircularProgressIndicator.adaptive(),
              const SizedBox(height: 20),
              ValueListenableBuilder(
                valueListenable: valueNotifier.progressIndex,
                builder: (context, index, _) => ProgressTabBar(
                  onTabChanged: (i) {
                    valueNotifier.changeProgressIndex(i);
                    fetchDailyDataForWeeks(-i);
                  },
                  titles: [
                    LocaleKeys.progress_this_week.tr(),
                    LocaleKeys.progress_last_week.tr(),
                    LocaleKeys.progress_two_weeks_ago.tr(),
                    LocaleKeys.progress_three_weeks_ago.tr(),
                  ],
                  indexValue: index,
                ),
              ),
              const SizedBox(height: 17),
              sumCalsDailyData.isNotEmpty
                  ? CalsSumChart(data: sumCalsDailyData.reversed.toList())
                  : const CircularProgressIndicator.adaptive(),
              const SizedBox(height: 20),
              model != null ? BmiChart(bmi: model!.bmi) : const CircularProgressIndicator.adaptive(),
            ],
          ),
        ),
      ),
    );
  }
}
