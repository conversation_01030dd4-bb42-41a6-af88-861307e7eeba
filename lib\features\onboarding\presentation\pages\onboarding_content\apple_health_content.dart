import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Goal Selection Screen
class AppleHealthContent extends StatefulWidget {
  const AppleHealthContent({super.key});

  @override
  State<AppleHealthContent> createState() => _GoalSelectionContentState();
}

class _GoalSelectionContentState extends State<AppleHealthContent> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          centerContent: true,
          contentWidgets: [
            const Center(
                child: AppImage.asset(
              Assets.onboardingAppleHealthContainer,
              width: 300,
              height: 300,
            )),
            const SizedBox(height: 10),
            <PERSON><PERSON>(
              alignment: Alignment.centerRight,
              child: Text(
                LocaleKeys.onboarding_connect_to.tr(),
                style: context.textTheme.headlineLarge!.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.end,
              ),
            ),
            Align(
              alignment: Alignment.centerRight,
              child: Text(
                "Apple Health",
                style: context.textTheme.headlineLarge!.copyWith(fontWeight: FontWeight.bold),
                // textAlign: TextAlign.right,
              ),
            ),
            const SizedBox(height: 10),
            Row(
              children: [
                Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    LocaleKeys.onboarding_sync_your_daily_activity_between.tr(),
                    style:
                        context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w200, color: context.onSecondary.withAlpha(140)),
                    // textAlign: TextAlign.right,
                  ),
                ),
                RichText(
                  textAlign: TextAlign.center,
                  text: const TextSpan(
                    children: [
                      TextSpan(
                        text: ' Orange ',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFFFF6B35),
                        ),
                      ),
                      TextSpan(
                        text: 'AI',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Align(
              alignment: Alignment.centerRight,
              child: Text(
                LocaleKeys.onboarding_and_Apple_Health_for_more_comprehensive_data.tr(),
                style: context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w200, color: context.onSecondary.withAlpha(140)),
                // textAlign: TextAlign.right,
              ),
            ),
          ],
        );
      },
    );
  }
}

class ArrowDownPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();

    // Curved line from top-left to bottom
    path.moveTo(10, 10);
    path.lineTo(10, 40);
    path.lineTo(30, 40);

    // Arrow head
    path.moveTo(25, 35);
    path.lineTo(30, 40);
    path.lineTo(35, 35);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class ArrowUpPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();

    // Curved line from bottom-right to top-left
    path.moveTo(70, 50);
    path.lineTo(70, 20);
    path.lineTo(20, 20);

    // Arrow head pointing up
    path.moveTo(15, 25);
    path.lineTo(20, 20);
    path.lineTo(25, 25);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
