import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/utils/haptic_feedback_util.dart';
import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:throttling/throttling.dart';

class BirthDateSelector extends StatefulWidget {
  final int initialDay;
  final int initialMonth;
  final int initialYear;
  final ValueChanged<int> onDayChanged;
  final ValueChanged<int> onMonthChanged;
  final ValueChanged<int> onYearChanged;

  const BirthDateSelector({
    super.key,
    required this.initialDay,
    required this.initialMonth,
    required this.initialYear,
    required this.onDayChanged,
    required this.onMonthChanged,
    required this.onYearChanged,
  });

  @override
  State<BirthDateSelector> createState() => _BirthDateSelectorState();
}

class _BirthDateSelectorState extends State<BirthDateSelector> {
  late int selectedDay;
  late Month selectedMonth;
  late int selectedYear;
  late FixedExtentScrollController dayController;
  late FixedExtentScrollController monthController;
  late FixedExtentScrollController yearController;
  late Throttling dayThrottler;
  late Throttling monthThrottler;
  late Throttling yearThrottler;

  final List<String> days = List.generate(31, (index) => '${index + 1}');
final List<String> years = List.generate(83, (index) => '${2012 - index}');

  @override
  void initState() {
    super.initState();
    selectedDay = widget.initialDay;
    selectedMonth = Month.values[widget.initialMonth - 1]; // Convert from 1-based to 0-based index
    selectedYear = widget.initialYear;
    dayController = FixedExtentScrollController(initialItem: selectedDay - 1);
    monthController = FixedExtentScrollController(initialItem: selectedMonth.index);
    yearController = FixedExtentScrollController(initialItem: years.indexOf(selectedYear.toString()));

    // Initialize throttlers
    dayThrottler = Throttling(duration: const Duration(milliseconds: 300));
    monthThrottler = Throttling(duration: const Duration(milliseconds: 300));
    yearThrottler = Throttling(duration: const Duration(milliseconds: 300));
  }

  @override
  void dispose() {
    dayController.dispose();
    monthController.dispose();
    yearController.dispose();

    dayThrottler.close();
    monthThrottler.close();
    yearThrottler.close();
    super.dispose();
  }

  Widget buildWheel({
    required List<String> values,
    required FixedExtentScrollController controller,
    required ValueChanged<int> onSelectedItemChanged,
    required int selectedIndex,
    required Throttling throttler,
  }) {
    return Expanded(
      child: ListWheelScrollView.useDelegate(
        itemExtent: 40,
        diameterRatio: 1.5,
        physics: const FixedExtentScrollPhysics(),
        onSelectedItemChanged: (index) {
          HapticFeedbackUtil.mediumImpact();
          setState(() {
            // Update UI immediately for responsive feel
          });
           onSelectedItemChanged(index);
        },
        controller: controller,
        childDelegate: ListWheelChildBuilderDelegate(
          childCount: values.length,
          builder: (context, index) {
            final isSelected = index == selectedIndex;
            return Center(
              child: Text(
                values[index],
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? context.primaryColor : Colors.black,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Row(
            children: [
              Expanded(
                child: Center(
                  child: Text(
                    LocaleKeys.onboarding_year.tr(),
                    style: context.textTheme.titleMedium!.copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
              Expanded(
                child: Center(
                  child: Text(
                    LocaleKeys.onboarding_month.tr(),
                    style: context.textTheme.titleMedium!.copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
              Expanded(
                child: Center(
                  child: Text(
                    LocaleKeys.onboarding_day.tr(),
                    style: context.textTheme.titleMedium!.copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 200,
          child: Row(
            children: [
              buildWheel(
                values: years.reversed.toList(),
                controller: yearController,
                selectedIndex: years.indexOf(selectedYear.toString()),
                throttler: yearThrottler,
                onSelectedItemChanged: (index) {
                  setState(() {
                    selectedYear = int.parse(years[index]);
                  });
                  widget.onYearChanged(int.parse(years[index]));
                },
              ),
              Expanded(
                child: ListWheelScrollView.useDelegate(
                  itemExtent: 40,
                  diameterRatio: 1.5,
                  physics: const FixedExtentScrollPhysics(),
                  onSelectedItemChanged: (index) {
                    HapticFeedbackUtil.mediumImpact();
                    setState(() {
                      selectedMonth = Month.values[index];
                    });

                    // Use throttling to debounce rapid changes
                    monthThrottler.throttle(() {
                      widget.onMonthChanged(selectedMonth.value);
                    });
                  },
                  controller: monthController,
                  childDelegate: ListWheelChildBuilderDelegate(
                    childCount: Month.values.length,
                    builder: (context, index) {
                      final month = Month.values[index];
                      final isSelected = month == selectedMonth;
                      return Center(
                        child: Text(
                          month.localizedName,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            color: isSelected ? context.primaryColor : Colors.black,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              buildWheel(
                values: days,
                controller: dayController,
                selectedIndex: selectedDay - 1,
                throttler: dayThrottler,
                onSelectedItemChanged: (index) {
                  setState(() {
                    selectedDay = index + 1;
                  });
                  widget.onDayChanged(index + 1);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
