import 'package:cal/common/consts/app_keys.dart';
import 'package:cal/core/mobile_id_helper.dart';
import 'dart:io' show Platform;
import 'package:flutter/material.dart';
import 'package:cal/features/authentication/presentation/widgets/apple_sign_in_webview.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../models/auth_token_model.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cal/core/network/api_handler.dart';
import 'package:cal/core/network/http_client.dart';
import 'package:cal/common/utils/shared_preferences_helper.dart';

// Remote Data Source for Authentication
abstract class AuthenticationRemoteDataSource {
  Future<AuthTokenModel> signInWithApple({required BuildContext context});
  Future<AuthTokenModel> signInWithGoogle();
  Future<void> signOut();
}

class AuthenticationRemoteDataSourceImpl with ApiHandler implements AuthenticationRemoteDataSource {
  final FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;
  final HTTPClient httpClient;

  AuthenticationRemoteDataSourceImpl({
    required FirebaseAuth firebaseAuth,
    required GoogleSignIn googleSignIn,
    required this.httpClient,
  })  : _firebaseAuth = firebaseAuth,
        _googleSignIn = googleSignIn;
  @override
  Future<AuthTokenModel> signInWithApple({required BuildContext context}) async {
    try {
      if (Platform.isAndroid) {
        return await _signInWithAppleWeb();
      }

      // 🍎 Native iOS flow
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final oauthCredential = OAuthProvider('apple.com').credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      final userCredential = await _firebaseAuth.signInWithCredential(oauthCredential);
      final user = userCredential.user;

      final idTokenResult = await user?.getIdTokenResult();
      final tokenStr = idTokenResult?.token;
      final expiresAt = idTokenResult?.expirationTime;

      if (tokenStr == null || expiresAt == null) {
        throw Exception('Firebase returned null token or expiration.');
      }

      return AuthTokenModel(
        accessToken: tokenStr,
        expiresAt: expiresAt,
      );
    } catch (e, stackTrace) {
      debugPrintStack(label: '🍎 Sign-In Error', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<AuthTokenModel> _signInWithAppleWeb() async {
    try {
      // Use the AppleSignInWebView widget to handle web-based authentication
      // Note: This assumes a context is available; adjust based on actual usage
      final result = await Navigator.push(
        // ignore: null_check_on_nullable_type_parameter
        BuildContext as dynamic,
        MaterialPageRoute(
          builder: (context) => AppleSignInWebView(
            onSuccess: (authToken) {
              Navigator.pop(context, authToken);
            },
            onError: (error) {
              Navigator.pop(context, error);
            },
            httpClient: httpClient,
          ),
        ),
      );

      if (result is AuthTokenModel) {
        return result;
      } else if (result is String) {
        throw Exception('Apple Sign-In failed on Android: $result');
      } else {
        throw Exception('Unexpected result from Apple Sign-In WebView');
      }
    } catch (e) {
      throw Exception('Apple Sign-In on Android failed: $e');
    }
  }

  @override
  Future<AuthTokenModel> signInWithGoogle() async {
    try {
      // Sign in with Google
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        throw Exception('Google Sign-In was cancelled by user');
      }

      // Get Google authentication details
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Create Firebase credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _firebaseAuth.signInWithCredential(credential);

      final user = userCredential.user;
      if (user == null) {
        throw Exception('User not found after Google sign-in');
      }

      final idTokenResult = await user.getIdTokenResult();

      final String mobileId = await MobileIdHelper.getMobileId();
      final googleTokenData = {
        'token': googleAuth.idToken,
        'mobile_id': mobileId,
      };

      final apiResult = await handleApiCall(
        apiCall: () => httpClient.post(
          '/api/auth/google',
          data: googleTokenData,
          headers: {'Content-Type': 'application/json'},
        ),
      );

      apiResult.fold(
        (failure) {
          throw Exception('API call failed during Google Sign-In: $failure');
        },
        (response) {
          final token = response['token'];

          if (token != null && token is String) {
            ShPH.saveData(key: AppKeys.token, value: token);
          } else {
            throw Exception("Token not found in response.");
          }
        },
      );

      return AuthTokenModel(
        accessToken: idTokenResult.token!,
        expiresAt: idTokenResult.expirationTime!,
      );
    } catch (e) {
      throw Exception('Google Sign-In failed: $e');
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
      await _googleSignIn.signOut();
    } catch (e) {
      throw Exception('Sign-out failed: $e');
    }
  }
}
