import 'package:cal/core/local_models/exercise_model/exercise_model.dart';
import 'package:cal/features/home/<USER>/repositories/exercise_repository.dart';
import 'package:injectable/injectable.dart';

@injectable
class GetDailyExercisesUseCase {
  final HomeExerciseRepository repository;

  GetDailyExercisesUseCase(this.repository);

  Future<List<ExerciseModel>> call(DateTime date) async {
    return await repository.getAllExercises(date);
  }
}
