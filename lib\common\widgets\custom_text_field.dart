import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/theme/text_theme.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class CustomTextField extends StatelessWidget {
  CustomTextField({
    super.key,
    required this.controller,
    required this.hint,
    this.height,
    this.width,
    this.onTap,
    this.radios,
    this.textAlign,
    this.sufIcon,
    this.contentPadding,
    this.fontSized,
    this.keyboardType,
    this.readOnly,
    this.obSecure,
    this.onChanged,
    this.preIcon,
    this.validator,
    this.mainColor,
    this.hintStyle,
    this.filledColor,
    this.mnLine,
    this.mxLine,
    this.borderWidth,
    this.focusNode,
    this.onEditingComplete,
    this.onFieldSubmitted,
    this.textInputAction,
  });

  final TextEditingController controller;
  final Widget? preIcon;
  final String hint;
  final ValueChanged<String>? onChanged;
  final Widget? sufIcon;
  final bool? obSecure;
  final bool? readOnly;
  final double? height;
  final double? width;
  final double? borderWidth;
  final double? radios;
  final double? fontSized;
  final TextAlign? textAlign;
  final TextInputType? keyboardType;
  final EdgeInsetsGeometry? contentPadding;
  final Color? mainColor;
  final TextStyle? hintStyle;
  Function()? onTap;
  Color? filledColor;
  int? mnLine;
  int? mxLine;
  FocusNode? focusNode;
  final FormFieldValidator<String>? validator;
  final void Function()? onEditingComplete;
  final void Function(String)? onFieldSubmitted;
  final TextInputAction? textInputAction;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      textInputAction: textInputAction,
      onFieldSubmitted: onFieldSubmitted,
      onEditingComplete: onEditingComplete,
      focusNode: focusNode,
      cursorColor: Colors.black,
      style: textTheme.titleSmall!.copyWith(color: context.onSecondary),
      textAlignVertical: TextAlignVertical.center,
      textAlign: textAlign ?? TextAlign.start,
      obscureText: obSecure ?? false,
      controller: controller,
      onChanged: onChanged,
      onTap: onTap,
      minLines: mnLine,
      maxLines: mxLine,
      readOnly: readOnly ?? false,
      keyboardType: keyboardType ?? TextInputType.emailAddress,
      validator: validator,
      onTapOutside: (PointerDownEvent event) {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      decoration: InputDecoration(
        labelStyle: textTheme.titleSmall!.copyWith(color: context.onSecondary),
        filled: true,
        fillColor: filledColor ?? context.onPrimaryColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radios ?? 10),
          borderSide: BorderSide(color: context.onBackground, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: mainColor ?? context.error,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(radios ?? 10),
        ),
        errorStyle: textTheme.labelSmall!.copyWith(
          color: context.error,
          height: 0.8,
        ),
        contentPadding: contentPadding,
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: mainColor ?? context.onSecondary,
            width: borderWidth ?? 1.5,
          ),
          borderRadius: BorderRadius.circular(radios ?? 10),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: mainColor ?? context.onSecondary.withAlpha(50),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(radios ?? 10),
        ),
        suffixIcon: sufIcon,
        prefixIcon: preIcon,
        hintText: hint,
        hintStyle: hintStyle ?? textTheme.titleSmall!.copyWith(color: Colors.black.withAlpha(102), fontSize: 14),
      ),
    );
  }
}
