import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/features/onboarding/presentation/widgets/time_selector.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class MealsTimeCard extends StatefulWidget {
  const MealsTimeCard({
    super.key,
    this.image,
    required this.onTimeSelected,
    required this.title,
  });

  final String? image;
  final String title;
  final ValueChanged<String> onTimeSelected;

  @override
  State<MealsTimeCard> createState() => _MealsTimeCardState();
}

class _MealsTimeCardState extends State<MealsTimeCard> {
  String? selectedTime;

  Future<void> _pickTime(BuildContext context) async {
    final now = TimeOfDay.now();
    int hour = now.hourOfPeriod == 0 ? 12 : now.hourOfPeriod;
    int minute = now.minute;
    bool isAm = now.period == DayPeriod.am;

    final result = await showDialog<String>(
      context: context,
      builder: (_) => Dialog(
        backgroundColor: context.onPrimaryColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: StatefulBuilder(
          builder: (context, setDialogState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 20),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      LocaleKeys.onboarding_choose_meal_time.tr(),
                      textAlign: TextAlign.right,
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Divider(color: context.onSecondary),
                const SizedBox(height: 10),
                SizedBox(
                  height: 200,
                  child: TimeSelector(
                    initialHour: hour,
                    initialMinute: minute,
                    initialIsAm: isAm,
                    onHourChanged: (val) => setDialogState(() => hour = val),
                    onMinuteChanged: (val) => setDialogState(() => minute = val),
                    onAmPmChanged: (val) => setDialogState(() => isAm = val),
                  ),
                ),
                const SizedBox(height: 16),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Row(
                    children: [
                      TextButton(
                        onPressed: () {
                          final hourStr = hour.toString().padLeft(2, '0');
                          final minuteStr = minute.toString().padLeft(2, '0');
                          final amPm = isAm ? "AM" : "PM";
                          final timeStr = '$hourStr:$minuteStr $amPm';
                          widget.onTimeSelected(timeStr);
                          Navigator.of(context).pop('$hourStr:$minuteStr $amPm');
                        },
                        child: Text(
                          LocaleKeys.onboarding_submit.tr(),
                          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: Text(
                          LocaleKeys.onboarding_cancel.tr(),
                          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );

    if (result != null) {
      setState(() {
        selectedTime = result;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final displayTitle = selectedTime ?? widget.title;

    return AppGestureDetector(
      onTap: () => _pickTime(context),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
        width: context.screenWidth,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: context.onSecondary.withAlpha(120), width: 2),
          color: context.onPrimaryColor,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            AppImage.asset(Assets.onboardingPlate, size: 30, color: Colors.black.withAlpha(178)),
            const SizedBox(width: 12),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: AppText.bodyMedium(
                      displayTitle,
                      textAlign: TextAlign.start,
                      color: Colors.black.withAlpha(178),
                      fontWeight: FontWeight.w900,
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      color: context.primaryColor,
                    ),
                    child: Icon(Icons.add, color: context.onPrimaryColor, size: 30),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
