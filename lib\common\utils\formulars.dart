import 'package:cal/features/onboarding/enums.dart';
import 'package:easy_localization/easy_localization.dart';

class Formulars {
  /// 1. BMI (Body Mass Index)
  static double calculateBMI({required double weightKg, required double heightCm}) {
    double heightM = heightCm / 100;
    return weightKg / (heightM * heightM);
  }

  /// 2. BMR (Basal Metabolic Rate)
  static double calculateBMR({
    required double weightKg,
    required double heightCm,
    required int age,
    required bool isMale,
  }) {
    if (isMale) {
      return (10 * weightKg) + (6.25 * heightCm) - (5 * age) + 5;
    } else {
      return (10 * weightKg) + (6.25 * heightCm) - (5 * age) - 161;
    }
  }

  /// 3. TDEE (Total Daily Energy Expenditure)
  static double calculateTDEE({required double bmr, required ExerciseFrequency exerciseFrequency}) {
    final activityMultipliers = {
      ExerciseFrequency.low: 1.2,
      ExerciseFrequency.medium: 1.375,
      ExerciseFrequency.high: 1.55,
    };

    return bmr * (activityMultipliers[exerciseFrequency] ?? 1.2);
  }

  /// 4. Calorie Target by Goal
  static double calculateTargetCalories({
    required double tdee,
    required String goal, // 'lose' or 'gain'
    required double weeklyTargetKg,
  }) {
    double adjustment = (weeklyTargetKg * 7700) / 7;

    if (goal.toLowerCase() == 'lose') {
      return tdee - adjustment;
    } else if (goal.toLowerCase() == 'gain') {
      return tdee + adjustment;
    } else {
      return tdee; // maintenance
    }
  }

  /// 5. Macronutrient Distribution
  static Map<String, double> calculateMacronutrients(double totalCalories) {
    double protein = (totalCalories * 0.25) / 4;
    double carbs = (totalCalories * 0.50) / 4;
    double fat = (totalCalories * 0.25) / 9;

    return {
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
    };
  }

  static String formatLocalizedDate(DateTime date, String locale) {
    // Use intl to get localized month and year, while keeping day and year as digits
    final day = date.day.toString(); // stays English
    final year = date.year.toString(); // stays English

    final month = DateFormat.MMMM(locale).format(date); // e.g. يوليو or July

    return '$day $month, $year';
  }

  static String calculateWeightLossEndDate({
    required double totalKgToLose,
    required double kgPerWeek,
    required String locale,
  }) {
    if (totalKgToLose <= 0 || kgPerWeek <= 0) {
      DateTime.now();
    }

    final weeksNeeded = totalKgToLose / kgPerWeek;
    final daysNeeded = (weeksNeeded * 7).ceil(); // Round up to ensure enough time

    return formatLocalizedDate(DateTime.now().add(Duration(days: daysNeeded)), locale);
  }

  static double calculateHealthScore({
    required double bmi,
    required ExerciseFrequency exerciseFrequency, // "sedentary", "light", "moderate", "very_active"
    required double weeklyGoalKg, // positive = gain, negative = lose, 0 = maintain
    required double proteinPercent,
    required double carbsPercent,
    required double fatPercent,
    required double tdee,
    required int targetCalories,
    required Diet dietType, // "balanced", "vegetarian", "high_protein", "keto", "none"
  }) {
    int bmiScore = 0;
    if (bmi >= 18.5 && bmi <= 24.9) {
      bmiScore = 20;
    } else if (bmi >= 25 && bmi <= 29.9) {
      bmiScore = 15;
    } else if (bmi >= 30 && bmi <= 34.9) {
      bmiScore = 10;
    } else {
      bmiScore = 5;
    }

    int activityScore = switch (exerciseFrequency) {
      ExerciseFrequency.high => 15,
      ExerciseFrequency.medium => 10,
      ExerciseFrequency.low => 5,
      // ignore: unreachable_switch_case
      _ => 2, // sedentary or unknown
    };

    int goalScore = 0;
    double absGoal = weeklyGoalKg.abs();
    if (weeklyGoalKg == 0 || absGoal <= 0.6) {
      goalScore = 10;
    } else if (absGoal <= 1.2) {
      goalScore = 7;
    } else if (absGoal <= 1.8) {
      goalScore = 5;
    } else {
      goalScore = 0;
    }

    int inRange = 0;
    if (proteinPercent >= 15 && proteinPercent <= 25) inRange++;
    if (carbsPercent >= 45 && carbsPercent <= 55) inRange++;
    if (fatPercent >= 20 && fatPercent <= 35) inRange++;

    int macroScore = switch (inRange) {
      3 => 15,
      2 => 10,
      1 => 5,
      _ => 0,
    };

    int calorieDiff = (tdee.toInt() - targetCalories).abs();
    int calorieScore = 0;
    if (calorieDiff <= 250) {
      calorieScore = 10;
    } else if (calorieDiff <= 500) {
      calorieScore = 7;
    } else if (calorieDiff <= 750) {
      calorieScore = 5;
    } else {
      calorieScore = 2;
    }

    int dietScore = switch (dietType) {
      Diet.eatEverything => 10,
      Diet.pescetarian => 9,
      Diet.vegetarian => 8,
      Diet.vegan => 7,
      Diet.keto => 5,
      // ignore: unreachable_switch_case
      _ => 5, // fallback/default for safety
    };

    int totalScore = bmiScore + activityScore + goalScore + macroScore + calorieScore + dietScore;

    // Normalize to 1–10 scale
    return ((totalScore / 80) * 9 + 1).clamp(1.0, 10.0);
  }

  static double proteinGramsToPercent({
    required double proteinGrams,
    required int totalCalories,
  }) {
    final double proteinCalories = proteinGrams * 4;
    return (proteinCalories / totalCalories) * 100;
  }

  static double carbsGramsToPercent({
    required double carbsGrams,
    required int totalCalories,
  }) {
    final double carbsCalories = carbsGrams * 4;
    return (carbsCalories / totalCalories) * 100;
  }

  static double fatGramsToPercent({
    required double fatGrams,
    required int totalCalories,
  }) {
    final double fatCalories = fatGrams * 9;
    return (fatCalories / totalCalories) * 100;
  }

  static int calculateAge({
    required int birthDay,
    required int birthMonth,
    required int birthYear,
  }) {
    final today = DateTime.now();
    int age = today.year - birthYear;

    // Check if the birthday hasn't occurred yet this year
    if (today.month < birthMonth || (today.month == birthMonth && today.day < birthDay)) {
      age--;
    }

    return age;
  }
}
